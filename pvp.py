# pvp.py
import discord
from discord import Embed, app_commands
from discord.ext import tasks
import asyncio
import random
from config import bot
from data import monsters, equipments, player_monsters, player_decks, player_stats, save_player_data, equipment_points, get_monster_points
from utils import calculate_bonuses, calculate_total_weight
import logging
from leaderboard import update_pvp_leaderboard

# Configuration des logs pour déboguer
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PVP_LEADERBOARD_CHANNEL_ID = 1372384501522501642  # Canal pour le classement PvP
active_battles = set()  # Ensemble pour suivre les joueurs en combat

def roll_d20():
    return random.randint(1, 20)

def roll_attack_die(attack_str):
    num, sides = attack_str.split('d')
    return random.randint(1, int(sides))

def roll_dice(dice_str):
    num, sides = dice_str.split('d')
    return random.randint(1, int(sides))

# Fonction pour calculer les bonus HP et énergie par tour
def calculate_turn_bonuses(fighter, deck):
    hp_bonus = 0
    energy_bonus = 0

    # Bonus des équipements
    for item in deck:
        if "hp_per_turn" in item and item["hp_per_turn"] > 0 and item["name"] != "Blood Ring":
            hp_bonus += item["hp_per_turn"]
        if "energy_per_turn" in item and item["name"] not in ["Energy Aura", "Frost Ring", "Energy Ring"] and item["energy_per_turn"] > 0:
            energy_bonus += item["energy_per_turn"]

    # Bonus de Blood Ring
    blood_ring_count = sum(1 for item in deck if item["name"] == "Blood Ring")
    hp_bonus += blood_ring_count * 2

    # Bonus de Red Flower
    red_flower_count = sum(1 for item in deck if item["name"] == "Red Flower")
    hp_bonus += red_flower_count * 1

    # Bonus de Frost Ring
    frost_ring_count = sum(1 for item in deck if item["name"] == "Frost Ring")
    energy_bonus += frost_ring_count * 1

    # Bonus d'Energy Ring
    if any(item["name"] == "Energy Ring" for item in deck):
        energy_bonus += 1

    # Bonus des effets de sorts
    if fighter["divinity_active"]:
        hp_bonus += 3
        energy_bonus += 3
    if fighter["healing_spring_turns"] > 0:
        hp_bonus += 7
    if "shield_of_life" in fighter["status_effects"] and fighter["status_effects"]["shield_of_life"]["active"]:
        hp_bonus += fighter["status_effects"]["shield_of_life"]["hp_per_turn"]
    if fighter["infinity_aura_active"]:
        hp_bonus += 2
        energy_bonus += 2
    if fighter["energy_aura_active"]:
        energy_bonus += 2

    return hp_bonus, energy_bonus

# Fonction pour formater la description d'un sort
def format_spell_description(spell):
    name = spell["name"]
    if name == "Absorb":
        return f" (Damage: {spell['damage']}, Heal: {spell['heal']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Angel of Fire":
        return f" (Damage: {spell['damage']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Divinity":
        return f" (Heal/Turn: {spell['heal_per_turn']}, Energy/Turn: {spell['energy_gain_per_turn']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Ice Bolt":
        return f" (Damage: {spell['damage']}, Disable Spells: {spell['disable_spell_turns']} turn, Energy Cost: {spell['energy_cost']})"
    elif name == "Shadow Ball":
        return f" (Damage: {spell['damage']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Soul Drain":
        return f" (Drain Energy: {spell['energy_drain']}, Gain Energy: {spell['energy_restore']}, HP Cost: {spell.get('hp_cost', 0)})"
    elif name == "He Potion":
        return f" (Heal: {spell['heal']}, Energy Gain: {spell['energy_restore']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Potion of Life":
        return f" (Heal: {spell['heal']}, Energy Gain: {spell['energy_restore']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Healing Spring":
        return f" (Heal: {spell['heal']}, Heal/Turn: {spell['heal_per_turn']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Desenchant":
        return f" (Reduce Attack: {spell['attack_reduction']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Energy Drain":
        return f" (Drain Energy: {spell['energy_drain']}, Gain Energy: {spell['energy_restore']}, HP Cost: {spell.get('hp_cost', 0)})"
    elif name == "Energy Potion":
        return f" (Energy Gain: {spell['energy_restore']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Fountain":
        return f" (Heal: {spell['heal']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Holy Dice":
        return f" (Heal: 1d12, Energy Cost: {spell['energy_cost']})"
    elif name == "Dagger Strike":
        return f" (Damage: 1d20, Energy Cost: {spell['energy_cost']})"
    elif name == "Holy Heal":
        return f" (Heal: {spell['heal']}, Energy Cost: {spell['energy_cost']})"
    elif name == "HP Potion":
        return f" (Heal: {spell['heal']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Energy Aura":
        return f" (Energy/Turn: {spell['energy_per_turn']}, HP Cost: {spell.get('hp_cost', 0)})"
    elif name == "Immolate":
        return f" (Damage: {spell['damage']}, +{spell['extra_damage']} for {spell['extra_turns']} turns, Energy Cost: {spell['energy_cost']})"
    elif name == "Lightning Bolt":
        return f" (Damage: {spell['damage']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Fire Ball":
        return f" (Damage: {spell['damage']}, +{spell['extra_damage']} for {spell['extra_turns']} turns, Energy Cost: {spell['energy_cost']})"
    elif name == "Shield of Life":
        return f" (HP/Turn: {spell['hp_per_turn']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Nova":
        return f" (Damage: {spell['damage']}, Disable Spells: {spell['disable_spell_turns']} turn, Energy Cost: {spell['energy_cost']})"
    elif name == "Acid Bomb":
        return f" (Damage: {spell['damage']}, +{spell['extra_damage']} for {spell['extra_turns']} turns, Energy Cost: {spell['energy_cost']})"
    elif name == "Restoration":
        return f" (Energy Gain: {spell['energy_restore']}, HP Cost: {spell.get('hp_cost', 0)})"
    elif name == "Secret Soul":
        return f" (Damage: 1d10, Energy Cost: {spell['energy_cost']})"
    elif name == "Shield Breaker":
        return f" (Reduce Defence: {spell['defence_reduction']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Infinity Aura":
        return f" (HP/Turn: {spell['hp_per_turn']}, Energy/Turn: {spell['energy_per_turn']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Meteor":
        return f" (Damage: {spell['damage']}, Energy Drain: {spell['energy_drain']}, Energy Cost: {spell['energy_cost']})"
    elif name == "Immunity":
        return f" (Grants immunity to damage next opponent turn, Energy Cost: {spell['energy_cost']})"
    return ""

@app_commands.command(name="player_battle", description="Challenge another player to a PvP battle")
@app_commands.describe(opponent="The player to challenge")
async def player_battle(interaction: discord.Interaction, opponent: discord.User):
    challenger = interaction.user
    channel = interaction.channel  # Utiliser le canal où la commande est exécutée
    fight_channel = None  # Initialiser pour la gestion dans finally
    start_message = None

    # Vérifications des erreurs
    if challenger.id == opponent.id:
        await interaction.response.send_message("You cannot challenge yourself!", ephemeral=True)
        return
    if challenger.id not in player_monsters or opponent.id not in player_monsters:
        await interaction.response.send_message("Both players must have a monster assigned! Contact an admin with /monster.", ephemeral=True)
        return
    if challenger.id in active_battles or opponent.id in active_battles:
        await interaction.response.send_message("One of the players is already in a battle!", ephemeral=True)
        return

    # Message de défi avec mention @opponent
    embed = Embed(title="PvP Challenge!", color=discord.Color.purple())
    embed.set_thumbnail(url=player_monsters[challenger.id]["image_url"])
    embed.add_field(
        name="Challenge",
        value=f"{opponent.mention}, you have been challenged to a battle by {challenger.mention}!\n\nReact with ✅ to accept or ❌ to decline (2 minutes).",
        inline=False
    )
    await interaction.response.send_message(content=f"{opponent.mention}", embed=embed)
    challenge_message = await interaction.original_response()
    await challenge_message.add_reaction("✅")
    await challenge_message.add_reaction("❌")

    def check(reaction, user):
        return user == opponent and reaction.message.id == challenge_message.id and str(reaction.emoji) in ["✅", "❌"]

    try:
        reaction, user = await bot.wait_for("reaction_add", timeout=120.0, check=check)
        if str(reaction.emoji) == "❌":
            await challenge_message.delete()
            await channel.send(f"{opponent.display_name} declined the challenge from {challenger.display_name}.")
            return
        else:
            # Supprimer le message d'invitation après acceptation
            try:
                await challenge_message.delete()
            except discord.errors.Forbidden:
                logger.error(f"Bot lacks permission to delete invitation message in channel {channel.id}.")
            except discord.errors.NotFound:
                logger.warning(f"Invitation message was already deleted in channel {channel.id}.")
            except Exception as e:
                logger.error(f"Unexpected error while deleting invitation message: {e}")
    except asyncio.TimeoutError:
        await challenge_message.delete()
        await channel.send(f"{opponent.display_name} did not respond to the challenge from {challenger.display_name}.")
        return

    # Marquer les joueurs comme étant en combat
    active_battles.add(challenger.id)
    active_battles.add(opponent.id)
    logger.info(f"Players {challenger.id} and {opponent.id} added to active_battles.")

    try:
        # Créer un canal temporaire pour le combat
        guild = channel.guild
        battlefield_number = 1
        while True:
            battlefield_name = f"pvp-battlefield-{battlefield_number}"
            if not any(ch.name == battlefield_name for ch in guild.text_channels):
                break
            battlefield_number += 1

        # Vérifier les permissions du bot pour créer et supprimer le canal
        bot_member = guild.get_member(bot.user.id)
        if not bot_member:
            logger.error("Bot member not found in guild.")
            await channel.send("Error: Bot cannot find itself in the server. Please contact an admin.")
            return
        guild_permissions = guild.me.guild_permissions
        if not guild_permissions.manage_channels:
            logger.error("Bot lacks 'Manage Channels' permission in guild.")
            await channel.send("Error: Bot lacks permission to create or delete channels. Please contact an admin.")
            return

        fight_channel = await guild.create_text_channel(battlefield_name)
        start_message = await channel.send(f"{challenger.mention} vs {opponent.mention}: The PvP battle has started in {fight_channel.mention}!")

        # Initialiser les données des joueurs
        player1 = {
            "user": challenger,
            "monster": player_monsters[challenger.id],
            "deck": player_decks.get(challenger.id, []),
            "stats": player_stats.get(challenger.id, {"pvp_wins": 0, "pvp_losses": 0, "pvp_points": 0})
        }
        player2 = {
            "user": opponent,
            "monster": player_monsters[opponent.id],
            "deck": player_decks.get(opponent.id, []),
            "stats": player_stats.get(opponent.id, {"pvp_wins": 0, "pvp_losses": 0, "pvp_points": 0})
        }

        # S'assurer que les stats des joueurs sont complètes
        for player in [player1, player2]:
            if not all(key in player["stats"] for key in ["pvp_wins", "pvp_losses", "pvp_points"]):
                player["stats"] = {"pvp_wins": 0, "pvp_losses": 0, "pvp_points": 0}
                logger.warning(f"Initialized missing stats for player {player['user'].id}")

        # Calculer les bonus pour chaque joueur
        p1_attack_bonus, p1_hp_bonus, p1_energy_bonus, p1_defence_bonus, p1_hp_per_turn, p1_energy_per_turn, p1_prevent_freeze = calculate_bonuses(player1["deck"])
        p2_attack_bonus, p2_hp_bonus, p2_energy_bonus, p2_defence_bonus, p2_hp_per_turn, p2_energy_per_turn, p2_prevent_freeze = calculate_bonuses(player2["deck"])

        p1_hp_per_turn = sum(item.get("hp_per_turn", 0) for item in player1["deck"] if item["name"] != "Blood Ring")
        p2_hp_per_turn = sum(item.get("hp_per_turn", 0) for item in player2["deck"] if item["name"] != "Blood Ring")

        # Initialiser les entités de combat
        fighter1 = {
            "hp": player1["monster"]["hp"] + p1_hp_bonus,
            "max_hp": player1["monster"]["hp"] + p1_hp_bonus,
            "energy": player1["monster"]["energy"] + p1_energy_bonus,
            "attack": player1["monster"]["attack"],
            "attack_bonus": p1_attack_bonus,
            "defence": player1["monster"]["defence"] + p1_defence_bonus,
            "deck": player1["deck"],
            "spell_counts": {item["name"]: sum(1 for i in player1["deck"] if i["name"] == item["name"] and i["type"] == "Spell") for item in player1["deck"] if item["type"] == "Spell"},
            "used_spells": {},
            "angel_of_fire_turns": 0,
            "divinity_active": False,
            "healing_spring_turns": 0,
            "spells_disabled": 0,
            "status_effects": {},
            "energy_aura_active": False,
            "fire_ball_turns": 0,
            "immolate_turns": 0,
            "infinity_aura_active": False,
            "immune": False,
            "prevent_freeze": p1_prevent_freeze
        }
        fighter2 = {
            "hp": player2["monster"]["hp"] + p2_hp_bonus,
            "max_hp": player2["monster"]["hp"] + p2_hp_bonus,
            "energy": player2["monster"]["energy"] + p2_energy_bonus,
            "attack": player2["monster"]["attack"],
            "attack_bonus": p2_attack_bonus,
            "defence": player2["monster"]["defence"] + p2_defence_bonus,
            "deck": player2["deck"],
            "spell_counts": {item["name"]: sum(1 for i in player2["deck"] if i["name"] == item["name"] and i["type"] == "Spell") for item in player2["deck"] if item["type"] == "Spell"},
            "used_spells": {},
            "angel_of_fire_turns": 0,
            "divinity_active": False,
            "healing_spring_turns": 0,
            "spells_disabled": 0,
            "status_effects": {},
            "energy_aura_active": False,
            "fire_ball_turns": 0,
            "immolate_turns": 0,
            "infinity_aura_active": False,
            "immune": False,
            "prevent_freeze": p2_prevent_freeze
        }

        # Appliquer les bonus initiaux de Frost Ring
        fighter1["energy"] += sum(1 for item in fighter1["deck"] if item["name"] == "Frost Ring") * 3
        fighter2["energy"] += sum(1 for item in fighter2["deck"] if item["name"] == "Frost Ring") * 3

        # Déterminer qui commence
        p1_roll = roll_d20()
        p2_roll = roll_d20()
        await fight_channel.send(f"{challenger.mention} rolled a **{p1_roll}**!\n{opponent.mention} rolled a **{p2_roll}**!")
        player1_turn = p1_roll >= p2_roll
        await fight_channel.send(f"{challenger.display_name if player1_turn else opponent.display_name} starts the battle!")

        # Boucle de combat
        while fighter1["hp"] > 0 and fighter2["hp"] > 0:
            current_fighter = fighter1 if player1_turn else fighter2
            opponent_fighter = fighter2 if player1_turn else fighter1
            current_player = player1 if player1_turn else player2
            opponent_player = player2 if player1_turn else player1
            current_p_hp_per_turn = p1_hp_per_turn if player1_turn else p2_hp_per_turn
            current_p_energy_per_turn = p1_energy_per_turn if player1_turn else p2_energy_per_turn
            current_color = discord.Color.blue() if player1_turn else discord.Color.red()

            # Appliquer les effets passifs
            for equipment in current_fighter["deck"]:
                if "hp_per_turn" in equipment and equipment["hp_per_turn"] > 0 and equipment["name"] != "Blood Ring":
                    current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + equipment["hp_per_turn"])
                if "energy_per_turn" in equipment and equipment["name"] != "Energy Aura" and equipment["name"] not in ["Frost Ring", "Energy Ring"] and equipment["energy_per_turn"] > 0:
                    current_fighter["energy"] += equipment["energy_per_turn"]

            # Gestion des effets de brûlure/poison
            if "burn" in current_fighter["status_effects"]:
                burn_damage = current_fighter["status_effects"]["burn"]["damage"]
                if not current_fighter["immune"]:
                    current_fighter["hp"] -= burn_damage
                else:
                    await fight_channel.send(f"{current_player['user'].display_name}'s Immunity blocks the burn damage!")
                current_fighter["status_effects"]["burn"]["turns"] -= 1
                await fight_channel.send(f"{current_player['user'].display_name}'s monster takes {burn_damage} burn damage!" if not current_fighter["immune"] else "")
                if current_fighter["status_effects"]["burn"]["turns"] <= 0:
                    del current_fighter["status_effects"]["burn"]

            if "burn" in opponent_fighter["status_effects"]:
                burn_damage = opponent_fighter["status_effects"]["burn"]["damage"]
                if not opponent_fighter["immune"]:
                    opponent_fighter["hp"] -= burn_damage
                else:
                    await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity blocks the burn damage!")
                opponent_fighter["status_effects"]["burn"]["turns"] -= 1
                await fight_channel.send(f"{opponent_player['user'].display_name}'s monster takes {burn_damage} burn damage!" if not opponent_fighter["immune"] else "")
                if opponent_fighter["status_effects"]["burn"]["turns"] <= 0:
                    del opponent_fighter["status_effects"]["burn"]

            # Tour du joueur
            hp_bonus_this_turn = 0
            energy_bonus_this_turn = 0
            extra_damage_this_turn = 0

            attack_roll = roll_attack_die(current_fighter["attack"])
            if any(item["name"] == "Crystal Sword" for item in current_fighter["deck"]):
                opponent_fighter["energy"] = max(0, opponent_fighter["energy"] - 2)
                await fight_channel.send(f"{current_player['user'].display_name}'s Crystal Sword burns 2 energy from {opponent_player['user'].display_name}!")

            if current_fighter["infinity_aura_active"]:
                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + 2)
                current_fighter["energy"] += 2
                hp_bonus_this_turn += 2
                energy_bonus_this_turn += 2
            if "shield_of_life" in current_fighter["status_effects"] and current_fighter["status_effects"]["shield_of_life"]["active"]:
                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + current_fighter["status_effects"]["shield_of_life"]["hp_per_turn"])
                hp_bonus_this_turn += current_fighter["status_effects"]["shield_of_life"]["hp_per_turn"]
                await fight_channel.send(f"{current_player['user'].display_name} gains {current_fighter['status_effects']['shield_of_life']['hp_per_turn']} HP from Shield of Life!")
            blood_ring_count = sum(1 for item in current_fighter["deck"] if item["name"] == "Blood Ring")
            current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + blood_ring_count * 2)
            hp_bonus_this_turn += blood_ring_count * 2
            frost_ring_count = sum(1 for item in current_fighter["deck"] if item["name"] == "Frost Ring")
            current_fighter["energy"] += frost_ring_count * 1
            energy_bonus_this_turn += frost_ring_count * 1
            if any(item["name"] == "Energy Ring" for item in current_fighter["deck"]):
                current_fighter["energy"] += 1
                energy_bonus_this_turn += 1
            if current_fighter["energy_aura_active"]:
                current_fighter["energy"] += 2
                energy_bonus_this_turn += 2
            total_attack = attack_roll + current_fighter["attack_bonus"]
            if current_fighter["angel_of_fire_turns"] > 0:
                total_attack += 10
                extra_damage_this_turn += 10
                current_fighter["angel_of_fire_turns"] -= 1
            if current_fighter["fire_ball_turns"] > 0:
                total_attack += 3
                extra_damage_this_turn += 3
                current_fighter["fire_ball_turns"] -= 1
            if current_fighter["immolate_turns"] > 0:
                total_attack += 2
                extra_damage_this_turn += 2
                current_fighter["immolate_turns"] -= 1
            if "poison" in opponent_fighter["status_effects"]:
                poison_damage = opponent_fighter["status_effects"]["poison"]["damage"]
                total_attack += poison_damage
                opponent_fighter["status_effects"]["poison"]["turns"] -= 1
                if opponent_fighter["status_effects"]["poison"]["turns"] <= 0:
                    del opponent_fighter["status_effects"]["poison"]
            if current_fighter["divinity_active"]:
                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + 3)
                current_fighter["energy"] += 3
            if "healing_spring_turns" in current_fighter and current_fighter["healing_spring_turns"] > 0:
                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + 7)
                current_fighter["healing_spring_turns"] -= 1

            if not opponent_fighter["immune"]:
                opponent_fighter["hp"] -= max(0, total_attack - opponent_fighter["defence"])
            else:
                await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity blocks the damage!")

            show_spell_box = False
            player_spells = [item for item in current_fighter["deck"] if item["type"] == "Spell"]
            available_spells = []
            for spell in player_spells:
                used_count = current_fighter["used_spells"].get(spell["name"], 0)
                max_count = current_fighter["spell_counts"].get(spell["name"], 0)
                if spell["name"] == "Absorb" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Absorb", "damage": 10, "energy_cost": 10, "heal": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Angel of Fire" and used_count < max_count and current_fighter["energy"] >= 15:
                    available_spells.append({"name": "Angel of Fire", "damage": 12, "energy_cost": 15, "dot": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Divinity" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Divinity", "heal_per_turn": 3, "energy_gain_per_turn": 3, "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Ice Bolt" and used_count < max_count and current_fighter["energy"] >= 7:
                    available_spells.append({"name": "Ice Bolt", "damage": 5, "energy_cost": 7, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                elif spell["name"] == "Shadow Ball" and used_count < max_count and current_fighter["energy"] >= 8:
                    available_spells.append({"name": "Shadow Ball", "damage": 10, "energy_cost": 8, "image_url": spell["image_url"]})
                elif spell["name"] == "Soul Drain" and used_count < max_count and current_fighter["hp"] >= 10:
                    available_spells.append({"name": "Soul Drain", "energy_drain": 15, "energy_restore": 15, "hp_cost": 10, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "He Potion" and used_count < max_count:
                    available_spells.append({"name": "He Potion", "heal": 5, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Potion of Life" and used_count < max_count:
                    available_spells.append({"name": "Potion of Life", "heal": 10, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Healing Spring" and used_count < max_count and current_fighter["energy"] >= 8:
                    available_spells.append({"name": "Healing Spring", "heal": 7, "heal_per_turn": 7, "heal_turns": 2, "energy_cost": 8, "image_url": spell["image_url"]})
                elif spell["name"] == "Desenchant" and used_count < max_count and current_fighter["energy"] >= 7:
                    available_spells.append({"name": "Desenchant", "attack_reduction": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                elif spell["name"] == "Energy Drain" and used_count < max_count and current_fighter["hp"] >= 5:
                    available_spells.append({"name": "Energy Drain", "energy_drain": 5, "energy_restore": 5, "hp_cost": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Energy Potion" and used_count < max_count:
                    available_spells.append({"name": "Energy Potion", "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Fountain" and used_count < max_count and current_fighter["energy"] >= 5:
                    available_spells.append({"name": "Fountain", "heal": 10, "energy_cost": 5, "image_url": spell["image_url"]})
                elif spell["name"] == "Holy Dice" and used_count < max_count and current_fighter["energy"] >= 6:
                    available_spells.append({"name": "Holy Dice", "heal_dice": "1d12", "energy_cost": 6, "image_url": spell["image_url"]})
                elif spell["name"] == "Dagger Strike" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Dagger Strike", "damage_dice": "1d20", "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Holy Heal" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Holy Heal", "heal": 30, "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "HP Potion" and used_count < max_count:
                    available_spells.append({"name": "HP Potion", "heal": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Energy Aura" and used_count < max_count and current_fighter["hp"] >= 6:
                    available_spells.append({"name": "Energy Aura", "energy_per_turn": 2, "hp_cost": 6, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Immolate" and used_count < max_count and current_fighter["energy"] >= 7:
                    available_spells.append({"name": "Immolate", "damage": 3, "extra_damage": 2, "extra_turns": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                elif spell["name"] == "Lightning Bolt" and used_count < max_count and current_fighter["energy"] >= 5:
                    available_spells.append({"name": "Lightning Bolt", "damage": 6, "energy_cost": 5, "image_url": spell["image_url"]})
                elif spell["name"] == "Fire Ball" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Fire Ball", "damage": 10, "extra_damage": 3, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Shield of Life" and used_count < max_count and current_fighter["energy"] >= 6:
                    available_spells.append({"name": "Shield of Life", "hp_per_turn": 2, "energy_cost": 6, "image_url": spell["image_url"]})
                elif spell["name"] == "Nova" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Nova", "damage": 10, "energy_cost": 10, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                elif spell["name"] == "Acid Bomb" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Acid Bomb", "damage": 5, "extra_damage": 5, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Restoration" and used_count < max_count and current_fighter["hp"] >= 5:
                    available_spells.append({"name": "Restoration", "energy_restore": 10, "hp_cost": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                elif spell["name"] == "Secret Soul" and used_count < max_count and current_fighter["energy"] >= 6:
                    available_spells.append({"name": "Secret Soul", "damage_dice": "1d10", "energy_cost": 6, "image_url": spell["image_url"]})
                elif spell["name"] == "Shield Breaker" and used_count < max_count and current_fighter["energy"] >= 4:
                    available_spells.append({"name": "Shield Breaker", "defence_reduction": 1, "energy_cost": 4, "image_url": spell["image_url"]})
                elif spell["name"] == "Infinity Aura" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Infinity Aura", "hp_per_turn": 2, "energy_per_turn": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                elif spell["name"] == "Meteor" and used_count < max_count and current_fighter["energy"] >= 20:
                    available_spells.append({"name": "Meteor", "damage": 20, "energy_drain": 10, "energy_cost": 20, "image_url": spell["image_url"]})
                elif spell["name"] == "Immunity" and used_count < max_count and current_fighter["energy"] >= 10:
                    available_spells.append({"name": "Immunity", "energy_cost": 10, "image_url": spell["image_url"]})

            if available_spells and current_fighter["spells_disabled"] == 0:
                show_spell_box = True
                spell_embed = Embed(title=f"{current_player['user'].display_name}, it's your turn!", color=current_color)
                spell_embed.set_thumbnail(url=current_player["monster"]["image_url"])
                spell_extra_damage = 0
                if current_fighter["angel_of_fire_turns"] > 0:
                    spell_extra_damage += 10
                if current_fighter["fire_ball_turns"] > 0:
                    spell_extra_damage += 3
                if current_fighter["immolate_turns"] > 0:
                    spell_extra_damage += 2
                if "poison" in opponent_fighter["status_effects"]:
                    spell_extra_damage += opponent_fighter["status_effects"]["poison"]["damage"]
                hp_bonus_total = current_p_hp_per_turn + (3 if current_fighter["divinity_active"] else 0) + hp_bonus_this_turn
                if "healing_spring_turns" in current_fighter and current_fighter["healing_spring_turns"] > 0:
                    hp_bonus_total += 7
                spell_embed.add_field(
                    name="Stats",
                    value=(
                        f"**Damage:** {attack_roll} + {current_fighter['attack_bonus']}{' +'+str(spell_extra_damage) if spell_extra_damage > 0 else ''}\n"
                        f"**HP:** {current_fighter['hp']}/{current_fighter['max_hp']} + {hp_bonus_total}\n"
                        f"**Energy:** {current_fighter['energy']} + {current_p_energy_per_turn + (3 if current_fighter['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                        f"**Defense:** {current_fighter['defence']}"
                    ),
                    inline=False
                )
                spell_embed.add_field(name="\u200b", value="\u200b", inline=False)
                # Calculer les bonus de l'adversaire
                opponent_hp_bonus, opponent_energy_bonus = calculate_turn_bonuses(opponent_fighter, opponent_player["deck"])
                # Ajuster l'énergie pour Crystal Sword
                if any(item["name"] == "Crystal Sword" for item in current_fighter["deck"]):
                    opponent_energy_bonus -= 2
                    opponent_energy_modifier_str = f" + {opponent_energy_bonus}" if opponent_energy_bonus > 0 else f" {opponent_energy_bonus}" if opponent_energy_bonus < 0 else ""
                else:
                    opponent_energy_modifier_str = f" + {opponent_energy_bonus}" if opponent_energy_bonus > 0 else " + 0"
                spell_embed.add_field(
                    name=f"{opponent_player['user'].display_name}",
                    value=f"**HP:** {opponent_fighter['hp']}/{opponent_fighter['max_hp']} + {opponent_hp_bonus}\n"
                          f"**Energy:** {opponent_fighter['energy']} {opponent_energy_modifier_str}\n"
                          f"**Defense:** {opponent_fighter['defence']}",
                    inline=False
                )
                spell_embed.add_field(name="\u200b", value="\u200b", inline=False)
                spell_text = "Choose a spell to use or skip with ❌:\n" + "\n".join(
                    f"{i+1}️⃣ - {s['name']}{format_spell_description(s)}"
                    for i, s in enumerate(available_spells)
                )
                spell_embed.add_field(name="Spells", value=spell_text, inline=False)
                spell_msg = await fight_channel.send(embed=spell_embed)
                for i in range(len(available_spells)):
                    await spell_msg.add_reaction(f"{i+1}️⃣")
                await spell_msg.add_reaction("❌")

                def spell_check(reaction, user):
                    return user == current_player['user'] and reaction.message.id == spell_msg.id and str(reaction.emoji) in [f"{i+1}️⃣" for i in range(len(available_spells))] + ["❌"]

                try:
                    reaction, user = await bot.wait_for("reaction_add", timeout=300.0, check=spell_check)
                    if str(reaction.emoji) != "❌":
                        spell_idx = int(str(reaction.emoji)[0]) - 1
                        spell = available_spells[spell_idx]
                        if (spell.get("energy_cost", 0) == 0 or current_fighter["energy"] >= spell.get("energy_cost", 0)) and (spell.get("hp_cost", 0) == 0 or current_fighter["hp"] >= spell.get("hp_cost", 0)):
                            current_fighter["energy"] = max(0, current_fighter["energy"] - spell.get("energy_cost", 0))
                            current_fighter["hp"] = max(0, current_fighter["hp"] - spell.get("hp_cost", 0))
                            current_fighter["used_spells"][spell["name"]] = current_fighter["used_spells"].get(spell["name"], 0) + 1
                            if "damage" in spell:
                                if not opponent_fighter["immune"]:
                                    opponent_fighter["hp"] -= spell["damage"]
                                else:
                                    await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity blocks the damage!")
                                if spell["name"] == "Absorb" and "heal" in spell:
                                    current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + spell["heal"])
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent and heals you!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Angel of Fire":
                                    current_fighter["angel_of_fire_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Ice Bolt":
                                    if not opponent_fighter["prevent_freeze"]:
                                        opponent_fighter["spells_disabled"] = spell["disable_spell_turns"]
                                        await fight_channel.send(embed=Embed(description=f"{current_player['user'].display_name} uses Ice Bolt, disabling the opponent's spells for the next turn!", color=current_color).set_image(url=spell["image_url"]))
                                    else:
                                        await fight_channel.send(f"{opponent_player['user'].display_name} is immune to freezing effects!")
                                elif spell["name"] == "Shadow Ball":
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Nova":
                                    if not opponent_fighter["prevent_freeze"]:
                                        opponent_fighter["spells_disabled"] = spell["disable_spell_turns"]
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent and disables their spells for the next turn!", color=current_color).set_image(url=spell["image_url"]))
                                    else:
                                        await fight_channel.send(f"{opponent_player['user'].display_name} is immune to freezing effects!")
                                elif spell["name"] == "Fire Ball":
                                    current_fighter["fire_ball_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Immolate":
                                    current_fighter["immolate_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Lightning Bolt":
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Acid Bomb":
                                    opponent_fighter["status_effects"]["poison"] = {"damage": spell["extra_damage"], "turns": spell["extra_turns"]}
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent and poisons them!", color=current_color).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Meteor":
                                    opponent_fighter["energy"] = max(0, opponent_fighter["energy"] - spell["energy_drain"])
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the opponent and burns their energy!", color=current_color).set_image(url=spell["image_url"]))
                            elif "heal_per_turn" in spell and "energy_gain_per_turn" in spell:
                                current_fighter["divinity_active"] = True
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} blesses you with divine energy!", color=current_color).set_image(url=spell["image_url"]))
                            elif "energy_drain" in spell and "energy_restore" in spell:
                                opponent_fighter["energy"] = max(0, opponent_fighter["energy"] - spell["energy_drain"])
                                current_fighter["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} drains the opponent's energy!", color=current_color).set_image(url=spell["image_url"]))
                            elif "heal" in spell and "energy_restore" in spell:
                                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + spell["heal"])
                                current_fighter["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your vitality!", color=current_color).set_image(url=spell["image_url"]))
                            elif "heal_per_turn" in spell and "heal" in spell:
                                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + spell["heal"])
                                current_fighter["healing_spring_turns"] = spell["heal_turns"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} heals you over time!", color=current_color).set_image(url=spell["image_url"]))
                            elif "attack_reduction" in spell:
                                opponent_fighter["attack_bonus"] -= spell["attack_reduction"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} reduces the opponent's attack by {spell['attack_reduction']}!", color=current_color).set_image(url=spell["image_url"]))
                            elif "defence_reduction" in spell:
                                opponent_fighter["defence"] = max(0, opponent_fighter["defence"] - spell["defence_reduction"])
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} reduces the opponent's defence by {spell['defence_reduction']}!", color=current_color).set_image(url=spell["image_url"]))
                            elif "energy_restore" in spell and "heal" not in spell and "hp_cost" in spell:
                                current_fighter["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your energy at the cost of {spell['hp_cost']} HP!", color=current_color).set_image(url=spell["image_url"]))
                            elif "energy_restore" in spell and "heal" not in spell and "hp_cost" not in spell:
                                current_fighter["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your energy!", color=current_color).set_image(url=spell["image_url"]))
                            elif "heal" in spell and "heal_per_turn" not in spell and "energy_restore" not in spell:
                                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + spell["heal"])
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} heals you!", color=current_color).set_image(url=spell["image_url"]))
                            elif "heal_dice" in spell:
                                heal_amount = roll_dice(spell["heal_dice"])
                                current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + heal_amount)
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} rolls a {heal_amount} and heals you!", color=current_color).set_image(url=spell["image_url"]))
                            elif "damage_dice" in spell:
                                damage = roll_dice(spell["damage_dice"])
                                if not opponent_fighter["immune"]:
                                    opponent_fighter["hp"] -= damage
                                else:
                                    await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity blocks the damage!")
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} rolls a {damage} and strikes the opponent!", color=current_color).set_image(url=spell["image_url"]))
                            elif "energy_per_turn" in spell and "hp_per_turn" in spell:
                                current_fighter["infinity_aura_active"] = True
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} activates an aura that restores 2 HP and 2 energy each damage roll!", color=current_color).set_image(url=spell["image_url"]))
                            elif "energy_per_turn" in spell and "hp_per_turn" not in spell:
                                current_fighter["energy_aura_active"] = True
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} activates an energy aura at the cost of {spell.get('hp_cost', 0)} HP!", color=current_color).set_image(url=spell["image_url"]))
                            elif spell["name"] == "Shield of Life":
                                current_fighter["status_effects"]["shield_of_life"] = {"hp_per_turn": spell["hp_per_turn"], "active": True}
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} activates! You’ll gain {spell['hp_per_turn']} HP each turn when rolling damage!", color=current_color).set_image(url=spell["image_url"]))
                            elif spell["name"] == "Immunity":
                                current_fighter["immune"] = True
                                await fight_channel.send(embed=Embed(description=f"{spell['name']} protects you from damage next turn!", color=current_color).set_image(url=spell["image_url"]))
                except asyncio.TimeoutError:
                    await fight_channel.send(f"{current_player['user'].mention} didn’t choose a spell in time. Moving to actions...")

            if current_fighter["hp"] <= 0 or opponent_fighter["hp"] <= 0:
                break

            if current_fighter["spells_disabled"] > 0:
                await fight_channel.send("You can't use spells this turn!")

            action_options = [
                {"name": "Attack: 5 dmg - Costs 3 Energy", "damage": 5, "energy_cost": 3, "index": 1},
                {"name": "Attack: 8 dmg - Costs 6 Energy", "damage": 8, "energy_cost": 6, "index": 2},
                {"name": "Attack: 13 dmg - Costs 10 Energy", "damage": 13, "energy_cost": 10, "index": 3},
                {"name": "Heal: 7 hp - Costs 4 Energy", "heal": 7, "energy_cost": 4, "index": 4},
                {"name": "Heal: 14 hp - Costs 10 Energy", "heal": 14, "energy_cost": 10, "index": 5},
                {"name": "Recharge: Gains 6 Energy - Costs 0 Energy", "energy_gain": 6, "energy_cost": 0, "index": 6}
            ]
            action_embed = Embed(title=f"{current_player['user'].display_name}, it's your turn!", color=current_color)
            action_embed.set_thumbnail(url=current_player["monster"]["image_url"])
            action_extra_damage = 0
            if current_fighter["angel_of_fire_turns"] > 0:
                action_extra_damage += 10
            if current_fighter["fire_ball_turns"] > 0:
                action_extra_damage += 3
            if current_fighter["immolate_turns"] > 0:
                action_extra_damage += 2
            if "poison" in opponent_fighter["status_effects"]:
                action_extra_damage += opponent_fighter["status_effects"]["poison"]["damage"]
            hp_bonus_total = current_p_hp_per_turn + (3 if current_fighter["divinity_active"] else 0) + hp_bonus_this_turn
            if "healing_spring_turns" in current_fighter and current_fighter["healing_spring_turns"] > 0:
                hp_bonus_total += 7
            if not show_spell_box:
                action_embed.add_field(
                    name="Stats",
                    value=(
                        f"**Damage:** {attack_roll} + {current_fighter['attack_bonus']}{' +'+str(action_extra_damage) if action_extra_damage > 0 else ''}\n"
                        f"**HP:** {current_fighter['hp']}/{current_fighter['max_hp']} + {hp_bonus_total}\n"
                        f"**Energy:** {current_fighter['energy']} + {current_p_energy_per_turn + (3 if current_fighter['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                        f"**Defense:** {current_fighter['defence']}"
                    ),
                    inline=False
                )
            else:
                action_embed.add_field(
                    name="Stats",
                    value=(
                        f"**HP:** {current_fighter['hp']}/{current_fighter['max_hp']} + {hp_bonus_total}\n"
                        f"**Energy:** {current_fighter['energy']} + {current_p_energy_per_turn + (3 if current_fighter['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                        f"**Defense:** {current_fighter['defence']}"
                    ),
                    inline=False
                )
            action_embed.add_field(name="\u200b", value="\u200b", inline=False)
            # Calculer les bonus de l'adversaire
            opponent_hp_bonus, opponent_energy_bonus = calculate_turn_bonuses(opponent_fighter, opponent_player["deck"])
            # Ajuster l'énergie pour Crystal Sword
            if any(item["name"] == "Crystal Sword" for item in current_fighter["deck"]):
                opponent_energy_bonus -= 2
                opponent_energy_modifier_str = f" + {opponent_energy_bonus}" if opponent_energy_bonus > 0 else f" {opponent_energy_bonus}" if opponent_energy_bonus < 0 else ""
            else:
                opponent_energy_modifier_str = f" + {opponent_energy_bonus}" if opponent_energy_bonus > 0 else " + 0"
            action_embed.add_field(
                name=f"{opponent_player['user'].display_name}",
                value=f"**HP:** {opponent_fighter['hp']}/{opponent_fighter['max_hp']} + {opponent_hp_bonus}\n"
                      f"**Energy:** {opponent_fighter['energy']}{opponent_energy_modifier_str}\n"
                      f"**Defense:** {opponent_fighter['defence']}",
                inline=False
            )
            action_embed.add_field(name="\u200b", value="\u200b", inline=False)
            action_text = "Pick a move by reacting with:\n" + "\n".join(
                f"{i+1}️⃣ - {a['name']}" for i, a in enumerate(action_options) if current_fighter["energy"] >= a["energy_cost"]
            )
            action_embed.add_field(name="Actions", value=action_text, inline=False)
            action_msg = await fight_channel.send(embed=action_embed)
            for i in range(len(action_options)):
                if current_fighter["energy"] >= action_options[i]["energy_cost"]:
                    await action_msg.add_reaction(f"{i+1}️⃣")

            def action_check(reaction, user):
                return user == current_player['user'] and reaction.message.id == action_msg.id and str(reaction.emoji) in [f"{i+1}️⃣" for i in range(len(action_options)) if current_fighter["energy"] >= action_options[i]["energy_cost"]]

            try:
                reaction, user = await bot.wait_for("reaction_add", timeout=300.0, check=action_check)
                action_idx = int(str(reaction.emoji)[0]) - 1
                action = action_options[action_idx]
                if current_fighter["energy"] >= action["energy_cost"]:
                    current_fighter["energy"] = max(0, current_fighter["energy"] - action["energy_cost"])
                    if "damage" in action:
                        if not opponent_fighter["immune"]:
                            opponent_fighter["hp"] -= action["damage"]
                            await fight_channel.send(f"{current_player['user'].display_name} uses **{action['name']}**!")
                        else:
                            await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity blocks the damage!")
                    elif "heal" in action:
                        current_fighter["hp"] = min(current_fighter["max_hp"], current_fighter["hp"] + action["heal"])
                        await fight_channel.send(f"{current_player['user'].display_name} uses **{action['name']}**!")
                    elif "energy_gain" in action:
                        current_fighter["energy"] += action["energy_gain"]
                        await fight_channel.send(f"{current_player['user'].display_name} uses **{action['name']}**!")
            except asyncio.TimeoutError:
                await fight_channel.send(f"{current_player['user'].mention} didn’t choose an action in time. Opponent’s turn!")

            if current_fighter["hp"] <= 0 or opponent_fighter["hp"] <= 0:
                break

            if current_fighter["spells_disabled"] > 0:
                current_fighter["spells_disabled"] -= 1
                if current_fighter["spells_disabled"] == 0:
                    await fight_channel.send(f"{current_player['user'].display_name}'s spells are no longer disabled!")

            if opponent_fighter["immune"]:
                opponent_fighter["immune"] = False
                await fight_channel.send(f"{opponent_player['user'].display_name}'s Immunity has worn off!")

            player1_turn = not player1_turn

        # Calculer les points
        p1_monster_points = get_monster_points(player1["monster"]["number"])
        p1_deck_points = sum(equipment_points.get(item["name"], 0) for item in player1["deck"])
        p1_total_points = p1_monster_points + p1_deck_points
        p2_monster_points = get_monster_points(player2["monster"]["number"])
        p2_deck_points = sum(equipment_points.get(item["name"], 0) for item in player2["deck"])
        p2_total_points = p2_monster_points + p2_deck_points

        # Mettre à jour les stats
        if fighter1["hp"] <= 0:
            await fight_channel.send(f"{player2['user'].display_name} has defeated {player1['user'].display_name}! +{p1_total_points} points.")
            player2["stats"]["pvp_wins"] += 1
            player2["stats"]["pvp_points"] += p1_total_points
            player1["stats"]["pvp_losses"] += 1
            player1["stats"]["pvp_points"] -= 1
        else:
            await fight_channel.send(f"{player1['user'].display_name} has defeated {player2['user'].display_name}! +{p2_total_points} points.")
            player1["stats"]["pvp_wins"] += 1
            player1["stats"]["pvp_points"] += p2_total_points
            player2["stats"]["pvp_losses"] += 1
            player2["stats"]["pvp_points"] -= 1

        player_stats[player1["user"].id] = player1["stats"]
        player_stats[player2["user"].id] = player2["stats"]
        save_player_data()
        await update_pvp_leaderboard()

    except Exception as e:
        logger.error(f"Unexpected error during battle between {challenger.display_name} and {opponent.display_name}: {e}")
        if fight_channel:
            await fight_channel.send(f"An error occurred during the battle: {str(e)}. Please contact an admin.")
    finally:
        # Retirer les joueurs de la liste des combats actifs
        active_battles.discard(challenger.id)
        active_battles.discard(opponent.id)
        logger.info(f"Players {challenger.id} and {opponent.id} removed from active_battles. Current active_battles: {active_battles}")

        # Supprimer le canal et le message de démarrage
        if fight_channel:
            try:
                await fight_channel.send("This battlefield will be destroyed in 2 minutes...")
                await asyncio.sleep(120)
                await fight_channel.delete()
                logger.info(f"Battle channel {fight_channel.name} deleted successfully.")
            except discord.errors.Forbidden:
                logger.error(f"Error: Bot lacks 'Manage Channels' permission to delete channel {fight_channel.name}.")
                await fight_channel.send("Error: Bot lacks permission to delete this channel. Please delete it manually or contact an admin.")
            except discord.errors.NotFound:
                logger.warning(f"Battle channel {fight_channel.name} was already deleted.")
            except Exception as e:
                logger.error(f"Unexpected error while deleting channel {fight_channel.name}: {e}")
                await fight_channel.send(f"Error: Failed to delete the channel due to {str(e)}. Please delete it manually or contact an admin.")

        if start_message:
            try:
                await start_message.delete()
                logger.info(f"Start message for battle between {challenger.display_name} and {opponent.display_name} deleted successfully.")
            except discord.errors.Forbidden:
                logger.error(f"Error: Bot lacks 'Manage Messages' permission to delete start message in channel {channel.name}.")
            except discord.errors.NotFound:
                logger.warning(f"Start message for battle between {challenger.display_name} and {opponent.display_name} was already deleted.")
            except Exception as e:
                logger.error(f"Unexpected error while deleting start message: {e}")

@app_commands.command(name="pvp_board", description="Check where to view the PvP leaderboard")
async def pvp_board(interaction: discord.Interaction):
    await interaction.response.send_message(f"Consultez le classement PvP dans le canal <#{PVP_LEADERBOARD_CHANNEL_ID}> !")
    message = await interaction.original_response()
    await asyncio.sleep(180)
    await message.delete()
