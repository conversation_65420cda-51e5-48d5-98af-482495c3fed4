# chest_event.py
import discord
from discord import Embed
from discord.ext import tasks
import asyncio
import random
from datetime import datetime, timedelta
from config import bot
from data import player_monsters, player_stats, save_player_data
import logging

# Configuration des logs
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ID du canal où les coffres apparaissent
CHEST_CHANNEL_ID = 1338676882719244391

# URLs des images
CHEST_IMAGE_URL = "https://i.imgur.com/rP2VYdq.jpeg"
GEM_IMAGE_URL = "https://i.imgur.com/axTx1Z0.jpeg"
EXP_IMAGE_URL = "https://i.imgur.com/RjAa53I.jpeg"

async def spawn_chest():
    """Envoie un embed de coffre dans le canal cible et gère l'interaction."""
    channel = bot.get_channel(CHEST_CHANNEL_ID)
    if not channel:
        logger.error(f"Canal avec ID {CHEST_CHANNEL_ID} non trouvé.")
        return

    # Vérifier les permissions
    permissions = channel.permissions_for(channel.guild.me)
    required_permissions = [
        permissions.send_messages,
        permissions.add_reactions,
        permissions.manage_messages
    ]
    if not all(required_permissions):
        logger.error(f"Permissions manquantes dans le canal {CHEST_CHANNEL_ID}: "
                     f"send_messages={permissions.send_messages}, "
                     f"add_reactions={permissions.add_reactions}, "
                     f"manage_messages={permissions.manage_messages}")
        return

    # Créer le premier embed (couleur bleue)
    embed = Embed(title="Chest Found", color=discord.Color.blue())
    embed.set_thumbnail(url=CHEST_IMAGE_URL)
    embed.add_field(name="\u200b", value="Interact with the\nemoji ✅ to open", inline=False)

    try:
        message = await channel.send(embed=embed)
        await message.add_reaction("✅")
        logger.info(f"Coffre envoyé dans le canal {CHEST_CHANNEL_ID} à {datetime.utcnow()}.")
    except discord.errors.Forbidden:
        logger.error(f"Le bot n'a pas les permissions pour envoyer un message ou ajouter une réaction dans le canal {CHEST_CHANNEL_ID}.")
        return
    except Exception as e:
        logger.error(f"Erreur inattendue lors de l'envoi du coffre: {e}")
        return

    def check_reaction(reaction, user):
        return (
            str(reaction.emoji) == "✅"
            and reaction.message.id == message.id
            and not user.bot
            and user.id in player_monsters
        )

    try:
        # Boucle pour attendre une réaction valide
        while True:
            try:
                # Attendre une réaction pendant 20 minutes
                reaction, user = await bot.wait_for('reaction_add', timeout=1200.0, check=lambda r, u: r.message.id == message.id and str(r.emoji) == "✅" and not u.bot)
                if user.id not in player_monsters:
                    # Envoyer un message éphémère (simulé par un message supprimé rapidement)
                    try:
                        error_message = await channel.send(f"{user.mention}, you need a monster to open the chest")
                        await asyncio.sleep(5)  # Garder le message 5 secondes
                        await error_message.delete()
                        logger.info(f"Message d'erreur envoyé à {user.display_name} pour tentative d'ouverture sans monstre.")
                    except discord.errors.Forbidden:
                        logger.error(f"Le bot n'a pas les permissions pour envoyer ou supprimer le message d'erreur dans le canal {CHEST_CHANNEL_ID}.")
                    continue  # Ignorer la réaction et continuer à attendre
                break  # Réaction valide, sortir de la boucle
            except asyncio.TimeoutError:
                # Timeout atteint, gérer plus bas
                raise

        # Supprimer le premier embed
        try:
            await message.delete()
            logger.info(f"Embed du coffre supprimé après interaction par {user.display_name}.")
        except discord.errors.NotFound:
            logger.warning("Message du coffre déjà supprimé.")
        except discord.errors.Forbidden:
            logger.error(f"Le bot n'a pas les permissions pour supprimer le message dans le canal {CHEST_CHANNEL_ID}.")

        # Déterminer la récompense
        reward = random.choices(
            ["exp", "gem"],
            weights=[70, 30],
            k=1
        )[0]

        player_id = user.id
        # Initialiser player_stats si le joueur n'existe pas
        if player_id not in player_stats:
            player_stats[player_id] = {
                "points": 0,
                "pvp_wins": 0,
                "pvp_losses": 0,
                "pvp_points": 0,
                "attributes": {
                    "available": 0,
                    "damage": 0,
                    "hp": 0,
                    "energy": 0,
                    "defence": 0
                },
                "gems": 0
            }

        # Créer l'embed de récompense
        reward_embed = Embed(title="Chest Opened!", color=discord.Color.green())
        if reward == "exp":
            exp_gained = random.randint(30, 175)
            player_stats[player_id]["points"] += exp_gained
            reward_message = f"{user.display_name}\nopened the chest\nand gained {exp_gained} Exp!"
            reward_embed.set_thumbnail(url=EXP_IMAGE_URL)
        else:
            player_stats[player_id]["gems"] += 1
            reward_message = f"{user.display_name}\nopened the chest\nand found 1 Gem 💎"
            reward_embed.set_thumbnail(url=GEM_IMAGE_URL)

        # Sauvegarder les données
        save_player_data()
        # Corriger la f-string en effectuant le remplacement en dehors
        log_message = reward_message.replace('\n', ' ')
        logger.info(f"Récompense attribuée à {user.display_name}: {log_message}")

        # Ajouter le message de récompense à l'embed
        reward_embed.add_field(name="\u200b", value=reward_message, inline=False)

        # Envoyer l'embed de récompense avec suppression après 4000 secondes
        try:
            reward_message_sent = await channel.send(embed=reward_embed)
            await asyncio.sleep(8000)  # Attendre 4000 secondes (~1h10 heures)
            try:
                await reward_message_sent.delete()
                logger.info(f"Embed de récompense supprimé après 40000 secondes.")
            except discord.errors.NotFound:
                logger.warning("Embed de récompense déjà supprimé.")
            except discord.errors.Forbidden:
                logger.error(f"Le bot n'a pas les permissions pour supprimer l'embed de récompense dans le canal {CHEST_CHANNEL_ID}.")
        except discord.errors.Forbidden:
            logger.error(f"Le bot n'a pas les permissions pour envoyer l'embed de récompense dans le canal {CHEST_CHANNEL_ID}.")
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'envoi de l'embed de récompense: {e}")

    except asyncio.TimeoutError:
        # Supprimer l'embed après 20 minutes si aucune interaction
        try:
            await message.delete()
            logger.info(f"Embed du coffre supprimé après timeout.")
        except discord.errors.NotFound:
            logger.warning("Message du coffre déjà supprimé.")
        except discord.errors.Forbidden:
            logger.error(f"Le bot n'a pas les permissions pour supprimer le message dans le canal {CHEST_CHANNEL_ID}.")
    except Exception as e:
        logger.error(f"Erreur inattendue lors de la gestion du coffre: {e}")

@tasks.loop(hours=1.0)
async def chest_spawn_task():
    """Tâche qui gère l'apparition des coffres dans chaque tranche horaire."""
    await bot.wait_until_ready()
    if bot.is_closed():
        return

    now = datetime.utcnow()
    # Générer un délai aléatoire dans les 60 minutes de la tranche
    random_minutes = random.randint(0, 59)
    random_seconds = random.randint(0, 59)
    spawn_time = now.replace(minute=0, second=0, microsecond=0) + timedelta(minutes=random_minutes, seconds=random_seconds)

    # Si spawn_time est dans le futur, attendre jusqu'à cet instant
    if spawn_time > now:
        wait_seconds = (spawn_time - now).total_seconds()
        logger.info(f"Prochain coffre prévu à {spawn_time}, attente de {wait_seconds} secondes.")
        await asyncio.sleep(wait_seconds)

    # Spawn du coffre
    await spawn_chest()

from discord import app_commands

@app_commands.command(name="test_chest", description="Test the chest spawn functionality")
async def test_chest(interaction: discord.Interaction):
    """Commande pour tester manuellement l'apparition d'un coffre."""
    await interaction.response.send_message("Spawning a test chest...", ephemeral=True)
    logger.info(f"Commande /test_chest exécutée par {interaction.user.display_name}.")
    await spawn_chest()