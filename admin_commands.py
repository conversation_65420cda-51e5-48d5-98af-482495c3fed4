# admin_commands.py
import discord
from discord import app_commands, Embed
from config import bot
from data import monsters, player_monsters, player_decks, equipments, save_player_data
from utils import calculate_bonuses, calculate_total_weight
import logging

logger = logging.getLogger(__name__)

@app_commands.command(name="monster", description="Assign a monster to a PLAYER (Admin only)")
@app_commands.describe(user="The PLAYER to assign the monster to", number="The monster's number (e.g., 001)")
async def monster(interaction: discord.Interaction, user: discord.User, number: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Error: Only administrators can use this command.", ephemeral=True)
        return

    selected_monster = next((m for m in monsters if m["number"] == number), None)
    if selected_monster:
        player_monsters[user.id] = selected_monster
        save_player_data()  # Sauvegarde après assignation du monstre
        embed = Embed(description=f"{interaction.user.display_name} has assigned monster {number} to {user.display_name}.", color=discord.Color.blue())
        embed.set_image(url=selected_monster["image_url"])
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Monster #{number} does not exist.", ephemeral=True)

@app_commands.command(name="equipment", description="Assign equipment and spells to a PLAYER’s deck (Admin only)")
@app_commands.describe(user="The PLAYER to assign equipment to")
async def equipment(interaction: discord.Interaction, user: discord.User):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Error: Only administrators can use this command.", ephemeral=True)
        return

    player_id = user.id
    if player_id not in player_monsters:
        await interaction.response.send_message(f"{user.display_name} must be assigned a monster first with /monster.", ephemeral=True)
        return

    # Charger le deck existant ou partir d'une liste vide
    current_deck = player_decks.get(player_id, [])
    total_weight = calculate_total_weight(current_deck)

    embed = Embed(title=f"Deck Creation for {user.display_name}", color=discord.Color.blue())
    embed.set_thumbnail(url=player_monsters[player_id]["image_url"])

    weapon_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Weapon") or "None"
    shield_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Shield") or "None"
    ring_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Ring") or "None (max 2)"
    equipment_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Equipment") or "None (max 3)"
    embed.add_field(name="Weapon", value=weapon_str, inline=False)
    embed.add_field(name="Shield", value=shield_str, inline=False)
    embed.add_field(name="Ring", value=ring_str, inline=False)
    embed.add_field(name="Equipment", value=equipment_str, inline=False)

    spell_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Spell") or "None"
    embed.add_field(name="Spells", value=spell_str, inline=False)

    embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

    if total_weight >= 10:
        await interaction.response.send_message(embed=embed, content=f"{user.display_name}'s deck has reached the maximum weight (10/10). Use /showequipment to view stats.")
        return

    def create_select(type_name, placeholder, start_idx=None, max_options=25):
        if type_name == "Spell" and start_idx is not None:
            filtered_items = [item for item in equipments if item["type"] == type_name][start_idx:start_idx + max_options]
        else:
            filtered_items = [item for item in equipments if item["type"] == type_name]
        
        # Vérification pour déboguer
        logger.info(f"Creating select for {type_name}, found {len(filtered_items)} items")
        for item in filtered_items:
            logger.info(f"  - {item['name']}")
        
        options = [
            discord.SelectOption(
                label=item["name"],
                description=f"{item['description']} (Weight: {item['weight']})"[:100],
                value=item["name"]
            ) for item in filtered_items
        ]
        return discord.ui.Select(
            placeholder=placeholder,
            options=options[:25],
            custom_id=f"{type_name.lower()}_{start_idx if start_idx is not None else 0}_select",
            disabled=total_weight >= 10
        )

    async def select_callback(interaction_callback: discord.Interaction):
        nonlocal total_weight
        selected_item_name = interaction_callback.data["values"][0]
        selected_item = next((i for i in equipments if i["name"] == selected_item_name), None)

        type_counts = {"Weapon": 0, "Shield": 0, "Ring": 0, "Equipment": 0}
        for deck_item in current_deck:
            if deck_item["type"] in type_counts:
                type_counts[deck_item["type"]] += 1
        if selected_item["type"] in type_counts:
            type_counts[selected_item["type"]] += 1

        if type_counts["Weapon"] > 1:
            await interaction_callback.response.send_message("Error: Only one weapon is allowed.", ephemeral=True)
            return
        if type_counts["Shield"] > 1:
            await interaction_callback.response.send_message("Error: Only one shield is allowed.", ephemeral=True)
            return
        if type_counts["Ring"] > 2:
            await interaction_callback.response.send_message("Error: Maximum of 2 rings allowed.", ephemeral=True)
            return
        if type_counts["Equipment"] > 3:
            await interaction_callback.response.send_message("Error: Maximum of 3 equipments allowed.", ephemeral=True)
            return

        new_total_weight = total_weight + selected_item["weight"]
        if new_total_weight > 10:
            await interaction_callback.response.send_message(f"Error: Total weight ({new_total_weight}) would exceed the limit of 10.", ephemeral=True)
            return

        current_deck.append(selected_item)
        player_decks[player_id] = current_deck  # Écraser l'ancien deck avec la nouvelle version
        total_weight = calculate_total_weight(current_deck)
        save_player_data()  # Sauvegarde après ajout d'un item

        weapon_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Weapon") or "None"
        shield_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Shield") or "None"
        ring_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Ring") or "None (max 2)"
        equipment_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Equipment") or "None (max 3)"
        embed.clear_fields()
        embed.add_field(name="Weapon", value=weapon_str, inline=False)
        embed.add_field(name="Shield", value=shield_str, inline=False)
        embed.add_field(name="Ring", value=ring_str, inline=False)
        embed.add_field(name="Equipment", value=equipment_str, inline=False)

        spell_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Spell") or "None"
        embed.add_field(name="Spells", value=spell_str, inline=False)

        embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

        if total_weight >= 10:
            weapon_select.disabled = True
            shield_select.disabled = True
            ring_select.disabled = True
            equipment_select.disabled = True
            spells_button.disabled = True
            await interaction_callback.response.edit_message(embed=embed, view=view, content=f"Maximum weight reached for {user.display_name}! Use /showequipment to view stats.")
        else:
            await interaction_callback.response.edit_message(embed=embed, view=view)

    async def spells_callback(interaction_callback: discord.Interaction):
        spell_items = [item for item in equipments if item["type"] == "Spell"]
        spell_pages = [spell_items[i:i + 25] for i in range(0, len(spell_items), 25)]
        current_page = 0

        spell_embed = Embed(title=f"Spell Selection for {user.display_name}", color=discord.Color.blue())
        spell_embed.set_thumbnail(url=player_monsters[player_id]["image_url"])
        spell_embed.add_field(name="Current Spells", value=spell_str, inline=False)
        spell_embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

        spell_select = create_select("Spell", f"Choose a spell (1-{min(25, len(spell_items))})", start_idx=0)
        spell_select.callback = select_callback

        async def prev_callback(spell_interaction: discord.Interaction):
            nonlocal current_page
            if current_page > 0:
                current_page -= 1
                spell_select.options = [
                    discord.SelectOption(
                        label=item["name"],
                        description=f"{item['description']} (Weight: {item['weight']})"[:100],
                        value=item["name"]
                    ) for item in spell_pages[current_page]
                ]
                spell_select.placeholder = f"Choose a spell ({current_page*25+1}-{min((current_page+1)*25, len(spell_items))})"
                prev_button.disabled = current_page == 0
                next_button.disabled = current_page >= len(spell_pages) - 1
                await spell_interaction.response.edit_message(embed=spell_embed, view=spell_view)

        async def next_callback(spell_interaction: discord.Interaction):
            nonlocal current_page
            if current_page < len(spell_pages) - 1:
                current_page += 1
                spell_select.options = [
                    discord.SelectOption(
                        label=item["name"],
                        description=f"{item['description']} (Weight: {item['weight']})"[:100],
                        value=item["name"]
                    ) for item in spell_pages[current_page]
                ]
                spell_select.placeholder = f"Choose a spell ({current_page*25+1}-{min((current_page+1)*25, len(spell_items))})"
                prev_button.disabled = current_page == 0
                next_button.disabled = current_page >= len(spell_pages) - 1
                await spell_interaction.response.edit_message(embed=spell_embed, view=spell_view)

        async def back_callback(spell_interaction: discord.Interaction):
            await spell_interaction.response.edit_message(embed=embed, view=view)

        prev_button = discord.ui.Button(label="Previous", style=discord.ButtonStyle.grey, disabled=True)
        next_button = discord.ui.Button(label="Next", style=discord.ButtonStyle.grey, disabled=len(spell_pages) <= 1)
        back_button = discord.ui.Button(label="Back to Equipment", style=discord.ButtonStyle.green)
        prev_button.callback = prev_callback
        next_button.callback = next_callback
        back_button.callback = back_callback

        spell_view = discord.ui.View()
        spell_view.add_item(spell_select)
        spell_view.add_item(prev_button)
        spell_view.add_item(next_button)
        spell_view.add_item(back_button)

        await interaction_callback.response.edit_message(embed=spell_embed, view=spell_view)

    weapon_select = create_select("Weapon", "Choose a weapon")
    shield_select = create_select("Shield", "Choose a shield")
    ring_select = create_select("Ring", "Choose a ring")
    equipment_select = create_select("Equipment", "Choose an equipment")
    spells_button = discord.ui.Button(label="Add Spells", style=discord.ButtonStyle.blurple)

    weapon_select.callback = select_callback
    shield_select.callback = select_callback
    ring_select.callback = select_callback
    equipment_select.callback = select_callback
    spells_button.callback = spells_callback

    view = discord.ui.View()
    view.add_item(weapon_select)
    view.add_item(shield_select)
    view.add_item(ring_select)
    view.add_item(equipment_select)
    view.add_item(spells_button)

    await interaction.response.send_message(embed=embed, view=view)

@app_commands.command(name="remove_equipment", description="Remove an equipment or spell from a PLAYER’s deck (Admin only)")
@app_commands.describe(user="The PLAYER to remove equipment from")
async def remove_equipment(interaction: discord.Interaction, user: discord.User):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Error: Only administrators can use this command.", ephemeral=True)
        return

    player_id = user.id
    if player_id not in player_monsters:
        await interaction.response.send_message(f"{user.display_name} must be assigned a monster first with /monster.", ephemeral=True)
        return
    if player_id not in player_decks or not player_decks[player_id]:
        await interaction.response.send_message(f"{user.display_name}'s deck is empty.", ephemeral=True)
        return

    current_deck = player_decks[player_id]
    total_weight = calculate_total_weight(current_deck)

    embed = Embed(title=f"Equipment Removal - {user.display_name}'s Deck", color=discord.Color.red())
    embed.set_thumbnail(url=player_monsters[player_id]["image_url"])
    equipment_str = "\n".join(f"- {item['name']} ({item['type']})" for item in current_deck if item["type"] != "Spell") or "None"
    embed.add_field(name="Stats", value=f"Removing items from {user.display_name}'s deck...", inline=False)
    embed.add_field(name="\u200b", value="\u200b", inline=False)
    embed.add_field(name="Equipment", value=equipment_str, inline=False)

    spell_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Spell") or "None"
    embed.add_field(name="Spells", value=spell_str, inline=False)

    embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

    options = [
        discord.SelectOption(
            label=item["name"],
            description=f"{item['description']} (Weight: {item['weight']})"[:100],
            value=f"{i}_{item['name']}"
        ) for i, item in enumerate(current_deck)
    ]

    select = discord.ui.Select(
        placeholder="Choose an equipment or spell to remove",
        options=options,
        custom_id="remove_select"
    )

    async def select_callback(interaction_callback: discord.Interaction):
        nonlocal total_weight
        selected_value = select.values[0]
        selected_index = int(selected_value.split('_')[0])
        item_to_remove = current_deck[selected_index]

        current_deck.remove(item_to_remove)
        player_decks[player_id] = current_deck  # Écraser avec le deck mis à jour
        total_weight = calculate_total_weight(current_deck)
        save_player_data()  # Sauvegarde après suppression d'un item

        equipment_str = "\n".join(f"- {item['name']} ({item['type']})" for item in current_deck if item["type"] != "Spell") or "None"
        embed.clear_fields()
        embed.add_field(name="Stats", value=f"Removing items from {user.display_name}'s deck...", inline=False)
        embed.add_field(name="\u200b", value="\u200b", inline=False)
        embed.add_field(name="Equipment", value=equipment_str, inline=False)

        spell_str = "\n".join(f"- {item['name']}" for item in current_deck if item["type"] == "Spell") or "None"
        embed.add_field(name="Spells", value=spell_str, inline=False)

        embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

        new_options = [
            discord.SelectOption(
                label=item["name"],
                description=f"{item['description']} (Weight: {item['weight']})"[:100],
                value=f"{i}_{item['name']}"
            ) for i, item in enumerate(current_deck)
        ]

        if new_options:
            select.options = new_options
            await interaction_callback.response.edit_message(embed=embed, view=view)
        else:
            view.clear_items()
            await interaction_callback.response.edit_message(embed=embed, view=view, content=f"{user.display_name}'s deck is empty! Use /equipment to add items.")

    select.callback = select_callback
    view = discord.ui.View()
    view.add_item(select)

    await interaction.response.send_message(embed=embed, view=view)

@app_commands.command(name="remove_all", description="Remove all items, spells, and monster from a player's deck (Admin only)")
@app_commands.describe(user="The player to remove all items and monster from")
async def remove_all(interaction: discord.Interaction, user: discord.User):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Error: Only administrators can use this command.", ephemeral=True)
        return

    player_id = user.id
    if player_id not in player_monsters and player_id not in player_decks:
        await interaction.response.send_message(f"{user.display_name} has no monster or deck assigned.", ephemeral=True)
        return

    # Supprimer le monstre et le deck
    if player_id in player_monsters:
        del player_monsters[player_id]
    if player_id in player_decks:
        del player_decks[player_id]

    # Sauvegarder les modifications
    save_player_data()

    # Journaliser l'action
    logger.info(f"Admin {interaction.user.display_name} removed all items and monster for {user.display_name}")

    # Créer un embed de confirmation
    embed = Embed(
        title="All Items and Monster Removed",
        description=f"All items, spells, and the monster have been removed from {user.display_name}'s deck.",
        color=discord.Color.red()
    )
    embed.set_footer(text=f"Action performed by {interaction.user.display_name}")

    await interaction.response.send_message(embed=embed)
