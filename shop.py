# shop.py
import discord
from discord import Embed
from discord.ext import tasks
import asyncio
import random
from datetime import datetime
from config import bot
from data import player_monsters, player_stats, equipments, equipment_points, save_player_data
import logging

# Configuration des logs
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ID des canaux
SHOP_CHANNEL_ID = 1372384454596759623
CARD_NOTIFICATION_CHANNEL_ID = 1372384556874993816

# URL de l'image du magasin
SHOP_IMAGE_URL = "https://i.imgur.com/ts64uvp.jpeg"

async def spawn_shop():
    """Envoie un embed de magasin dans le canal cible et gère les interactions."""
    channel = bot.get_channel(SHOP_CHANNEL_ID)
    if not channel:
        logger.error(f"Canal avec ID {SHOP_CHANNEL_ID} non trouvé.")
        return

    notification_channel = bot.get_channel(CARD_NOTIFICATION_CHANNEL_ID)
    if not notification_channel:
        logger.error(f"Canal de notification avec ID {CARD_NOTIFICATION_CHANNEL_ID} non trouvé.")
        return

    # Vérifier les permissions dans le canal du magasin
    permissions = channel.permissions_for(channel.guild.me)
    required_permissions = [
        permissions.send_messages,
        permissions.add_reactions,
        permissions.manage_messages,
        permissions.read_message_history
    ]
    if not all(required_permissions):
        logger.error(f"Permissions manquantes dans le canal {SHOP_CHANNEL_ID}: "
                     f"send_messages={permissions.send_messages}, "
                     f"add_reactions={permissions.add_reactions}, "
                     f"manage_messages={permissions.manage_messages}, "
                     f"read_message_history={permissions.read_message_history}")
        return

    # Vérifier les permissions dans le canal de notification
    notification_permissions = notification_channel.permissions_for(notification_channel.guild.me)
    if not notification_permissions.send_messages:
        logger.error(f"Le bot n'a pas la permission d'envoyer des messages dans le canal {CARD_NOTIFICATION_CHANNEL_ID}.")
        return

    # Créer l'embed du magasin
    embed = Embed(title="We have everything you need right here.", color=discord.Color.blue())
    embed.set_thumbnail(url=SHOP_IMAGE_URL)
    embed.add_field(name="\u200b", value="1️⃣ Draw 1 Cards  10 X 💎", inline=False)

    try:
        message = await channel.send(embed=embed)
        await message.add_reaction("1️⃣")
        logger.info(f"Magasin envoyé dans le canal {SHOP_CHANNEL_ID} à {datetime.utcnow()}.")
    except discord.errors.Forbidden:
        logger.error(f"Le bot n'a pas les permissions pour envoyer un message ou ajouter une réaction dans le canal {SHOP_CHANNEL_ID}.")
        return
    except Exception as e:
        logger.error(f"Erreur inattendue lors de l'envoi du magasin: {e}")
        return

    while True:
        try:
            # Attendre une réaction
            reaction, user = await bot.wait_for(
                'reaction_add',
                check=lambda r, u: r.message.id == message.id and str(r.emoji) == "1️⃣" and not u.bot
            )

            # Vérifier si le joueur a un monstre
            if user.id not in player_monsters:
                try:
                    error_message = await channel.send(f"{user.mention}, you need a monster to use the shop")
                    await asyncio.sleep(5)
                    await error_message.delete()
                    logger.info(f"Message d'erreur envoyé à {user.display_name} pour absence de monstre.")
                except discord.errors.Forbidden:
                    logger.error(f"Le bot n'a pas les permissions pour envoyer/supprimer le message d'erreur dans le canal {SHOP_CHANNEL_ID}.")
                continue

            # Vérifier si le joueur a assez de gems
            if user.id not in player_stats or player_stats[user.id].get("gems", 0) < 10:
                try:
                    error_message = await channel.send(f"{user.mention}, you don't have enough gems!")
                    await asyncio.sleep(5)
                    await error_message.delete()
                    logger.info(f"Message d'erreur envoyé à {user.display_name} pour gems insuffisants.")
                except discord.errors.Forbidden:
                    logger.error(f"Le bot n'a pas les permissions pour envoyer/supprimer le message d'erreur dans le canal {SHOP_CHANNEL_ID}.")
                continue

            # Supprimer l'embed actuel
            try:
                await message.delete()
                logger.info(f"Embed du magasin supprimé après interaction par {user.display_name}.")
            except discord.errors.NotFound:
                logger.warning("Message du magasin déjà supprimé.")
            except discord.errors.Forbidden:
                logger.error(f"Le bot n'a pas les permissions pour supprimer le message dans le canal {SHOP_CHANNEL_ID}.")

            # Déduire les gems
            player_stats[user.id]["gems"] -= 10
            if player_stats[user.id]["gems"] < 0:
                player_stats[user.id]["gems"] = 0  # Sécurité

            # Diviser les équipements en tiers
            equipment_with_points = [(eq, equipment_points.get(eq["name"], 0)) for eq in equipments]
            equipment_with_points.sort(key=lambda x: x[1])  # Trier par points
            total = len(equipment_with_points)
            tier_size = total // 3
            tier1 = equipment_with_points[:tier_size]  # Moins fortes
            tier2 = equipment_with_points[tier_size:2*tier_size]  # Moyennes
            tier3 = equipment_with_points[2*tier_size:]  # Plus fortes

            # Sélectionner un tier avec probabilités
            tiers = [tier1, tier2, tier3]
            weights = [60, 38, 2]
            selected_tier = random.choices(tiers, weights=weights, k=1)[0]
            card = random.choice(selected_tier)[0]  # Choisir une carte dans le tier

            # Envoyer la notification avec l'image
            try:
                image_url = card.get("image_url", "")
                if image_url:
                    await notification_channel.send(f"{user.mention} your drawing card\n{image_url}")
                    logger.info(f"Carte tirée '{card['name']}' envoyée pour {user.display_name} dans le canal {CARD_NOTIFICATION_CHANNEL_ID}.")
                else:
                    await notification_channel.send(f"{user.mention} your drawing card: {card['name']} (image non disponible)")
                    logger.warning(f"Carte '{card['name']}' tirée pour {user.display_name} sans image_url valide.")
            except discord.errors.Forbidden:
                logger.error(f"Le bot n'a pas les permissions pour envoyer la carte dans le canal {CARD_NOTIFICATION_CHANNEL_ID}.")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la carte: {e}")

            # Envoyer un message éphémère de redirection
            try:
                redirect_message = await channel.send(f"{user.mention}, check your card in <#1312212717905645579>!")
                await asyncio.sleep(5)
                await redirect_message.delete()
                logger.info(f"Message de redirection envoyé à {user.display_name} dans le canal {SHOP_CHANNEL_ID}.")
            except discord.errors.Forbidden:
                logger.error(f"Le bot n'a pas les permissions pour envoyer/supprimer le message de redirection dans le canal {SHOP_CHANNEL_ID}.")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi du message de redirection: {e}")

            # Sauvegarder les données
            save_player_data()
            logger.info(f"Achat effectué par {user.display_name}: 10 gems déduits, carte tirée: {card['name']}.")

            # Relancer un nouvel embed
            return await spawn_shop()

        except Exception as e:
            logger.error(f"Erreur inattendue lors de la gestion du magasin: {e}")
            break

@tasks.loop(count=1)
async def start_shop():
    """Démarre le magasin au lancement du bot."""
    await bot.wait_until_ready()
    if bot.is_closed():
        return
    logger.info("Démarrage du magasin permanent.")
    await spawn_shop()

from discord import app_commands

@app_commands.command(name="test_shop", description="Test the shop spawn functionality")
async def test_shop(interaction: discord.Interaction):
    """Commande pour tester manuellement l'apparition du magasin."""
    await interaction.response.send_message("Spawning a test shop...", ephemeral=True)
    logger.info(f"Commande /test_shop exécutée par {interaction.user.display_name}.")
    await spawn_shop()