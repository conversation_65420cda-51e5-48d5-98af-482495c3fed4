# leaderboard.py
import discord
from discord import Embed
import logging
from data import player_stats, save_player_data
from user_commands import calculate_season_level_and_exp
from config import bot

# Configuration des logs
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

LEADERBOARD_CHANNEL_ID = 1372384407427481620  # Canal pour le classement PvE
PVP_LEADERBOARD_CHANNEL_ID = 1372384501522501642  # Canal pour le classement PvP

async def update_leaderboard():
    """Met à jour le classement dans le canal dédié avec deux embeds (Top 1-10 et Top 11-20)."""
    channel = bot.get_channel(LEADERBOARD_CHANNEL_ID)
    if not channel:
        logger.error(f"Error: Leaderboard channel with ID {LEADERBOARD_CHANNEL_ID} not found.")
        return

    try:
        await channel.purge(limit=100)
    except discord.errors.Forbidden:
        logger.error(f"Error: <PERSON><PERSON> lacks permissions to delete messages in channel {LEADERBOARD_CHANNEL_ID}.")
        return
    except Exception as e:
        logger.error(f"Unexpected error while purging leaderboard channel: {e}")
        return

    if not player_stats:
        logger.info("No player stats available for leaderboard.")
        embed1 = Embed(title="Monster Battle Leaderboard (Top 1-10)", color=discord.Color.gold())
        embed1.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
        embed1.add_field(name="Rankings", value="No data", inline=False)
        embed2 = Embed(title="Monster Battle Leaderboard (Top 11-20)", color=discord.Color.gold())
        embed2.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
        embed2.add_field(name="Rankings", value="No data", inline=False)
        try:
            await channel.send(embed=embed1)
            await channel.send(embed=embed2)
            logger.info("Empty leaderboard embeds sent successfully.")
        except discord.errors.Forbidden:
            logger.error(f"Error: Bot lacks permissions to send messages in channel {LEADERBOARD_CHANNEL_ID}.")
        except Exception as e:
            logger.error(f"Unexpected error while sending empty leaderboard: {e}")
        return

    # Initialiser les stats manquantes
    for player_id, stats in player_stats.items():
        if not all(key in stats for key in ["wins", "losses", "points"]):
            stats.update({"wins": stats.get("wins", 0), "losses": stats.get("losses", 0), "points": stats.get("points", 0)})
            logger.warning(f"Initialized missing stats for player {player_id} in leaderboard.")
            save_player_data()

    # Trier les joueurs par points (descendant) et limiter à 20
    sorted_stats = sorted(player_stats.items(), key=lambda x: x[1].get("points", 0), reverse=True)[:20]

    # Diviser en deux groupes
    top_1_10 = sorted_stats[:10]
    top_11_20 = sorted_stats[10:]

    # Créer le premier embed (Top 1-10)
    embed1 = Embed(title="Monster Battle Leaderboard (Top 1-10)", color=discord.Color.gold())
    embed1.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
    leaderboard_text1 = ""
    for rank, (player_id, stats) in enumerate(top_1_10, start=1):
        try:
            player = await bot.fetch_user(player_id)
            player_name = player.display_name
        except discord.NotFound:
            player_name = f"Unknown Player ({player_id})"
        wins = stats.get("wins", 0)
        losses = stats.get("losses", 0)
        points = stats.get("points", 0)
        season_level, _, _ = calculate_season_level_and_exp(points)
        leaderboard_text1 += (
            f"{rank}. **{player_name}**\n"
            f"  Lv: {season_level}\n"
            f"  Wins: {wins}\n"
            f"  Losses: {losses}\n"
            f"  Exp: {points}\n\n"
        )
    for rank in range(len(top_1_10) + 1, 11):
        leaderboard_text1 += f"{rank}. -\n\n"
    embed1.add_field(name="Rankings", value=leaderboard_text1.strip() or "No data", inline=False)

    # Créer le deuxième embed (Top 11-20)
    embed2 = Embed(title="Monster Battle Leaderboard (Top 11-20)", color=discord.Color.gold())
    embed2.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
    leaderboard_text2 = ""
    for rank, (player_id, stats) in enumerate(top_11_20, start=11):
        try:
            player = await bot.fetch_user(player_id)
            player_name = player.display_name
        except discord.NotFound:
            player_name = f"Unknown Player ({player_id})"
        wins = stats.get("wins", 0)
        losses = stats.get("losses", 0)
        points = stats.get("points", 0)
        season_level, _, _ = calculate_season_level_and_exp(points)
        leaderboard_text2 += (
            f"{rank}. **{player_name}**\n"
            f"  Lv: {season_level}\n"
            f"  Wins: {wins}\n"
            f"  Losses: {losses}\n"
            f"  Exp: {points}\n\n"
        )
    for rank in range(max(11, len(top_11_20) + 11), 21):
        leaderboard_text2 += f"{rank}. -\n\n"
    embed2.add_field(name="Rankings", value=leaderboard_text2.strip() or "No data", inline=False)

    # Envoyer les deux embeds
    try:
        await channel.send(embed=embed1)
        await channel.send(embed=embed2)
        logger.info("Leaderboard embeds (Top 1-10 and Top 11-20) sent successfully.")
    except discord.errors.Forbidden:
        logger.error(f"Error: Bot lacks permissions to send messages in channel {LEADERBOARD_CHANNEL_ID}.")
    except Exception as e:
        logger.error(f"Unexpected error while sending leaderboard: {e}")

async def update_pvp_leaderboard():
    channel = bot.get_channel(PVP_LEADERBOARD_CHANNEL_ID)
    if not channel:
        logger.error(f"Error: Leaderboard channel with ID {PVP_LEADERBOARD_CHANNEL_ID} not found.")
        return

    try:
        await channel.purge(limit=100)
    except discord.errors.Forbidden:
        logger.error(f"Error: Bot lacks permissions to delete messages in channel {PVP_LEADERBOARD_CHANNEL_ID}.")
        return
    except Exception as e:
        logger.error(f"Unexpected error while purging leaderboard channel: {e}")
        return

    if not player_stats:
        embed = Embed(title="PvP Battle Leaderboard", color=discord.Color.gold())
        embed.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
        embed.add_field(name="Rankings", value="No data", inline=False)
        try:
            await channel.send(embed=embed)
        except discord.errors.Forbidden:
            logger.error(f"Error: Bot lacks permissions to send messages in channel {PVP_LEADERBOARD_CHANNEL_ID}.")
        except Exception as e:
            logger.error(f"Unexpected error while sending leaderboard: {e}")
        return

    # Trier les joueurs par points PvP
    sorted_stats = sorted(player_stats.items(), key=lambda x: x[1].get("pvp_points", 0), reverse=True)
    
    # Diviser en top 1-10 et top 11-20
    top_10 = sorted_stats[:10]
    next_10 = sorted_stats[10:20]
    
    # Créer le premier embed (Top 1-10)
    embed1 = Embed(title="PvP Battle Leaderboard (Top 1-10)", color=discord.Color.gold())
    embed1.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
    
    leaderboard_text1 = ""
    for rank, (player_id, stats) in enumerate(top_10, start=1):
        try:
            player = await bot.fetch_user(player_id)
            player_name = player.display_name
        except discord.NotFound:
            player_name = f"Unknown Player ({player_id})"
        pvp_wins = stats.get("pvp_wins", 0)
        pvp_losses = stats.get("pvp_losses", 0)
        pvp_points = stats.get("pvp_points", 0)
        leaderboard_text1 += (
            f"{rank}. **{player_name}**\n"
            f"   - Wins: {pvp_wins}\n"
            f"   - Losses: {pvp_losses}\n"
            f"   - Points: {pvp_points}\n\n"
        )
    
    embed1.add_field(name="Rankings", value=leaderboard_text1.strip() or "No data", inline=False)
    
    # Créer le deuxième embed (Top 11-20)
    embed2 = Embed(title="PvP Battle Leaderboard (Top 11-20)", color=discord.Color.gold())
    embed2.set_thumbnail(url="https://i.imgur.com/AnQCIIo.png")
    
    leaderboard_text2 = ""
    for rank, (player_id, stats) in enumerate(next_10, start=11):
        try:
            player = await bot.fetch_user(player_id)
            player_name = player.display_name
        except discord.NotFound:
            player_name = f"Unknown Player ({player_id})"
        pvp_wins = stats.get("pvp_wins", 0)
        pvp_losses = stats.get("pvp_losses", 0)
        pvp_points = stats.get("pvp_points", 0)
        leaderboard_text2 += (
            f"{rank}. **{player_name}**\n"
            f"   - Wins: {pvp_wins}\n"
            f"   - Losses: {pvp_losses}\n"
            f"   - Points: {pvp_points}\n\n"
        )
    
    embed2.add_field(name="Rankings", value=leaderboard_text2.strip() or "No data", inline=False)

    # Envoyer les deux embeds
    try:
        await channel.send(embeds=[embed1, embed2])
    except discord.errors.Forbidden:
        logger.error(f"Error: Bot lacks permissions to send messages in channel {PVP_LEADERBOARD_CHANNEL_ID}.")
    except Exception as e:
        logger.error(f"Unexpected error while sending leaderboard: {e}")