# commands.py
import discord
from config import bot
from combat import spawn_random_monster as spawn_normal_monster
from elite_combat import spawn_random_monster as spawn_elite_monster
from chest_event import spawn_chest, chest_spawn_task
from shop import spawn_shop, start_shop, test_shop
from admin_commands import monster, equipment, remove_equipment, remove_all
from user_commands import show_equipment, show_profile, monster_board
from pvp import player_battle, pvp_board
from data import load_player_data
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@bot.event
async def on_ready():
    print(f"{bot.user} is online!")
    load_player_data()
    logger.info("Player data loaded from JSON.")
    try:
        bot.tree.add_command(monster)
        bot.tree.add_command(equipment)
        bot.tree.add_command(remove_equipment)
        bot.tree.add_command(remove_all)
        bot.tree.add_command(show_equipment)
        bot.tree.add_command(show_profile)
        bot.tree.add_command(monster_board)
        bot.tree.add_command(player_battle)
        bot.tree.add_command(pvp_board)
        bot.tree.add_command(test_shop)
        synced = await bot.tree.sync()
        logger.info(f"Commands synced: {len(synced)} commands - {', '.join(cmd.name for cmd in synced)}")
        if not all(cmd in [cmd.name for cmd in synced] for cmd in ["player_battle", "pvp_board", "remove_all", "show_profile", "test_shop"]):
            logger.warning("Warning: One or more commands not found in synced commands, forcing sync again...")
            await bot.tree.sync()
        spawn_normal_monster.start()  # Démarre les monstres normaux
        spawn_elite_monster.start()   # Démarre les monstres élites
        chest_spawn_task.start()      # Démarre la tâche des coffres
        start_shop.start()            # Démarre le magasin
    except Exception as e:
        logger.error(f"Error syncing commands or starting tasks: {e}")