# data.py
import json
import os
import logging

# Configuration des logs pour déboguer
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Monster database
monsters = [
    {"number": "001", "hp": 55, "attack": "1d10", "energy": 27, "defence": 1, "image_url": "https://i.imgur.com/qVvJeG4.png"},
    {"number": "002", "hp": 55, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/4JJlMj8.png"},
    {"number": "003", "hp": 48, "attack": "1d8", "energy": 22, "defence": 0, "image_url": "https://i.imgur.com/1gkfwjO.png"},
    {"number": "004", "hp": 45, "attack": "1d9", "energy": 22, "defence": 0, "image_url": "https://i.imgur.com/j1QU4KM.png"},
    {"number": "005", "hp": 50, "attack": "1d7", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/ad92HJn.png"},
    {"number": "006", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/oyttqHc.png"},
    {"number": "007", "hp": 45, "attack": "1d7", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/mANIzqI.png"},
    {"number": "008", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/c140twL.png"},
    {"number": "009", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/YBOzgmN.png"},
    {"number": "010", "hp": 55, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/oKHb1NV.png"},
    {"number": "011", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/wdKgf6Y.png"},
    {"number": "012", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/rZretxN.png"},
    {"number": "013", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/lnRwt3a.png"},
    {"number": "014", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Bq6lfWN.png"},
    {"number": "015", "hp": 40, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/FfQPLK9.png"},
    {"number": "016", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/jIzR2lm.png"},
    {"number": "017", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/JQbsXXm.png"},
    {"number": "018", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/WEON744.png"},
    {"number": "019", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/XgphZPq.png"},
    {"number": "020", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/QIX24Y1.png"},
    {"number": "021", "hp": 50, "attack": "1d10", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/bmyTbDc.png"},
    {"number": "022", "hp": 47, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/k9P1k6U.png"},
    {"number": "023", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/pPGxYk1.png"},
    {"number": "024", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/uLvg9Zx.png"},
    {"number": "025", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/rtulhv6.png"},
    {"number": "026", "hp": 55, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/1wyZTa6.png"},
    {"number": "027", "hp": 50, "attack": "1d8", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/omIcV2I.png"},
    {"number": "028", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ASreuWJ.png"},
    {"number": "029", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/JdrSqeC.png"},
    {"number": "030", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/IxCfYWv.png"},
    {"number": "031", "hp": 55, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/lAzFIht.png"},
    {"number": "032", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/btVfj0F.png"},
    {"number": "033", "hp": 48, "attack": "1d7", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/iCygUJe.png"},
    {"number": "034", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/hbXKuSP.png"},
    {"number": "035", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/9N7put7.png"},
    {"number": "036", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/fB5ZTXy.png"},
    {"number": "037", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/Uu8d8xi.png"},
    {"number": "038", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/OvQZf2v.png"},
    {"number": "039", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/rq1LWgB.png"},
    {"number": "040", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/0liqJFq.png"},
    {"number": "041", "hp": 55, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/PwHQXSl.png"},
    {"number": "042", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/dhfkvSN.png"},
    {"number": "043", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ThS4zvL.png"},
    {"number": "044", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/pqnEm0V.png"},
    {"number": "045", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/mKi7XzD.png"},
    {"number": "046", "hp": 50, "attack": "1d9", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/GCOvFI5.png"},
    {"number": "047", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/kwnwUOS.png"},
    {"number": "048", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/xRsl83I.png"},
    {"number": "049", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/UM2OAlV.png"},
    {"number": "050", "hp": 55, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/W1kHSKw.png"},
    {"number": "051", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/4i4SJqK.png"},
    {"number": "052", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/M5uROo0.png"},
    {"number": "053", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/DXWKEs7.png"},
    {"number": "054", "hp": 45, "attack": "1d7", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/trdOvbw.png"},
    {"number": "055", "hp": 45, "attack": "1d8", "energy": 22, "defence": 0, "image_url": "https://i.imgur.com/8jw7Qk0.png"},
    {"number": "056", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/wV8BUL0.png"},
    {"number": "057", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/BQB2KXa.png"},
    {"number": "058", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Daqn8Og.png"},
    {"number": "059", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/R93jgZC.png"},
    {"number": "060", "hp": 45, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/hSQlKI8.png"},
    {"number": "061", "hp": 50, "attack": "1d10", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/XLRuBwv.png"},
    {"number": "062", "hp": 47, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/vFUw3eZ.png"},
    {"number": "063", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/aaA8AHw.png"},
    {"number": "064", "hp": 45, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/VCwPbo3.png"},
    {"number": "065", "hp": 40, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/PORG5xp.png"},
    {"number": "066", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/NVVEYbd.png"},
    {"number": "067", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/KKJpdk3.png"},
    {"number": "068", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/S0NjjUj.png"},
    {"number": "069", "hp": 45, "attack": "1d7", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/1qfOQ6F.png"},
    {"number": "070", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/au0gAX4.png"},
    {"number": "071", "hp": 45, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/KKJpdk3.png"},
    {"number": "072", "hp": 50, "attack": "1d9", "energy": 30, "defence": 1, "image_url": "https://i.imgur.com/r9ndq8a.png"},
    {"number": "073", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/HFPgTGh.png"},
    {"number": "074", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Bbaj4Ue.png"},
    {"number": "075", "hp": 50, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/oDWEz3N.png"},
    {"number": "076", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/vl48fm2.png"},
    {"number": "077", "hp": 45, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/utczCzf.png"},
    {"number": "078", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/VBiwtam.png"},
    {"number": "079", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/iiun8KK.png"},
    {"number": "080", "hp": 40, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/CyT93Z1.png"},
    {"number": "081", "hp": 45, "attack": "1d8", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/qvNu1yD.png"},
    {"number": "082", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/GnqWjMf.png"},
    {"number": "083", "hp": 55, "attack": "1d10", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/SAfRzVZ.png"},
    {"number": "084", "hp": 50, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/GsZ2Sao.png"},
    {"number": "085", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/0qOXZJQ.png"},
    {"number": "086", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/2juvgbY.png"},
    {"number": "087", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/by7XupM.png"},
    {"number": "088", "hp": 50, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/TxaXinf.png"},
    {"number": "089", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/oFfQQNs.png"},
    {"number": "090", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ppY08Dk.png"},
    {"number": "091", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/bVElU7l.png"},
    {"number": "092", "hp": 50, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/D2GcBZM.png"},
    {"number": "093", "hp": 45, "attack": "1d7", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/6LskeuU.png"},
    {"number": "094", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/qbZQRgq.png"},
    {"number": "095", "hp": 60, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/SdQF6Cx.png"},
    {"number": "096", "hp": 50, "attack": "1d7", "energy": 30, "defence": 1, "image_url": "https://i.imgur.com/mvOGYO7.png"},
    {"number": "097", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/03T8VIQ.png"},
    {"number": "098", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/9xl8zC2.png"},
    {"number": "099", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/aLLRvIM.png"},
    {"number": "100", "hp": 40, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/YYU3mpG.png"},
    {"number": "101", "hp": 55, "attack": "1d9", "energy": 27, "defence": 1, "image_url": "https://i.imgur.com/c1PJ57a.png"},
    {"number": "102", "hp": 48, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/jwsLb39.png"},
    {"number": "103", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ER9XtZI.png"},
    {"number": "104", "hp": 45, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/ZPh5dZU.png"},
    {"number": "105", "hp": 55, "attack": "1d11", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/OU5muL3.png"},
    {"number": "106", "hp": 48, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/RRvDSFI.png"},
    {"number": "107", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/7aKS79L.png"},
    {"number": "108", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/hWtQcTp.png"},
    {"number": "109", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/q86wcKA.png"},
    {"number": "110", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/YIYySp4.png"},
    {"number": "111", "hp": 47, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/LSE9PGE.png"},
    {"number": "112", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/Uk2Tmi6.png"},
    {"number": "113", "hp": 62, "attack": "1d8", "energy": 23, "defence": 2, "image_url": "https://i.imgur.com/jfJ3pMW.png"},
    {"number": "114", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/0sfIiix.png"},
    {"number": "115", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/qMaXA8Q.png"},
    {"number": "116", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/XaOcqJ9.png"},
    {"number": "117", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/U4kKNht.png"},
    {"number": "118", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/5nO8Vf7.png"},
    {"number": "119", "hp": 55, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Li6VsNg.png"},
    {"number": "120", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/qp9H6bq.png"},
    {"number": "121", "hp": 50, "attack": "1d9", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/vs0NeO7.png"},
    {"number": "122", "hp": 45, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/T8D1faH.png"},
    {"number": "123", "hp": 48, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/I3yna2G.png"},
    {"number": "124", "hp": 57, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/QwviszF.png"},
    {"number": "125", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/QD8ZaAU.png"},
    {"number": "126", "hp": 48, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ce7xptn.png"},
    {"number": "127", "hp": 55, "attack": "1d10", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/qx6ZEsA.png"},
    {"number": "128", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/kpeLYn4.png"},
    {"number": "129", "hp": 52, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/0Vyz7q0.png"},
    {"number": "130", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/tFtQYLN.png"},
    {"number": "131", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/2xOKAcC.png"},
    {"number": "132", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/lCrAVWl.png"},
    {"number": "133", "hp": 40, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/voFEGLs.png"},
    {"number": "134", "hp": 48, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Z0NU48f.png"},
    {"number": "135", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Ke1Se3R.png"},
    {"number": "136", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/f8yNYqR.png"},
    {"number": "137", "hp": 50, "attack": "1d8", "energy": 30, "defence": 2, "image_url": "https://i.imgur.com/Rcnupgf.png"},
    {"number": "138", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/OEWoFGg.png"},
    {"number": "139", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/gqmE8Yh.png"},
    {"number": "140", "hp": 52, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/0BU9hAG.png"},
    {"number": "141", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/nUGb9wv.png"},
    {"number": "142", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/O14kzpl.png"},
    {"number": "143", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/K6ELACs.png"},
    {"number": "144", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/yde0dRq.png"},
    {"number": "145", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/wazNIat.png"},
    {"number": "146", "hp": 45, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/SiDYqHf.png"},
    {"number": "147", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/DCUTG78.png"},
    {"number": "148", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/zB08pQA.png"},
    {"number": "149", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/5gDKpZl.png"},
    {"number": "150", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/J4E83GX.png"},
    {"number": "151", "hp": 45, "attack": "1d7", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ybOF3Gw.png"},
    {"number": "152", "hp": 47, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/JzLLNap.png"},
    {"number": "153", "hp": 57, "attack": "1d10", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/VCcM2YQ.png"},
    {"number": "154", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/J2nRkB5.png"},
    {"number": "155", "hp": 45, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/0vExUxh.png"},
    {"number": "156", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Snhs9Ze.png"},
    {"number": "157", "hp": 50, "attack": "1d8", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/p7iJqrp.png"},
    {"number": "158", "hp": 55, "attack": "1d11", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/Hq8Bn2B.png"},
    {"number": "159", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/NE6Jdhe.png"},
    {"number": "160", "hp": 45, "attack": "1d7", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/eGYfk1P.png"},
    {"number": "182", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/xWwrczn.png"},
    {"number": "200", "hp": 55, "attack": "1d10", "energy": 23, "defence": 1, "image_url": "https://i.imgur.com/93L0Uo9.png"},
    {"number": "201", "hp": 50, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/vpVNefl.png"},
    {"number": "202", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/RpAU0ht.png"},
    {"number": "203", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/joCEiWt.png"},
    {"number": "204", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/js5OxCI.png"},
    {"number": "205", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/jo5d0p4.png"},
    {"number": "206", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/7T1MQ3l.png"},
    {"number": "207", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/6enwvhf.png"},
    {"number": "208", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/t6xSgkJ.png"},
    {"number": "209", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/PnK6VBU.png"},
    {"number": "210", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/Cd1WKcf.png"},
    {"number": "211", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/y7osBrl.png"},
    {"number": "212", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/v1fdMFw.png"},
    {"number": "213", "hp": 55, "attack": "1d11", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/dWNw5ON.png"},
    {"number": "214", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/6uYaP0e.png"},
    {"number": "215", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/5lOfCfQ.png"},
    {"number": "216", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/wd83haf.png"},
    {"number": "217", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/SwDhbU5.png"},
    {"number": "218", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/GEOgZac.png"},
    {"number": "219", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/UY0UOIQ.png"},
    {"number": "220", "hp": 55, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/XdtQx0m.png"},
    {"number": "221", "hp": 55, "attack": "1d12", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/WIsSn4D.png"},
    {"number": "222", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/ATTQgR8.png"},
    {"number": "223", "hp": 47, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/D91Ufry.png"},
    {"number": "224", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/xtQq5lq.png"},
    {"number": "225", "hp": 60, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/1IvNVyt.png"},
    {"number": "226", "hp": 45, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/FiOdTDu.png"},
    {"number": "227", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/bSLhxfD.png"},
    {"number": "228", "hp": 47, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/CuQZbuJ.png"},
    {"number": "229", "hp": 55, "attack": "1d10", "energy": 27, "defence": 1, "image_url": "https://i.imgur.com/mnpgZCE.png"},
    {"number": "230", "hp": 55, "attack": "1d10", "energy": 27, "defence": 1, "image_url": "https://i.imgur.com/ZFARDYK.png"},
    {"number": "231", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/tjza4D7.png"},
    {"number": "232", "hp": 45, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/SpHiz6a.png"},
    {"number": "233", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/gBLaSWT.png"},
    {"number": "234", "hp": 50, "attack": "1d8", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/aSBuKyN.png"},
    {"number": "235", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/Astrzjc.png"},
    {"number": "236", "hp": 50, "attack": "1d9", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/oSDVBdu.png"},
    {"number": "237", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/o5wuqSV.png"},
    {"number": "238", "hp": 47, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/OGwCKNM.png"},
    {"number": "239", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/8idS6qZ.png"},
    {"number": "240", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/n6WU04p.png"},
    {"number": "241", "hp": 55, "attack": "1d11", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/hZ2M2dQ.png"},
    {"number": "242", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/xgaCX06.png"},
    {"number": "243", "hp": 45, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/dpghcsb.png"},
    {"number": "244", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/Vfn3dq2.png"},
    {"number": "245", "hp": 55, "attack": "1d10", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/wtHgHEq.png"},
    {"number": "246", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/k1iVSDp.png"},
    {"number": "247", "hp": 50, "attack": "1d9", "energy": 27, "defence": 0, "image_url": "https://i.imgur.com/ojyvvMZ.png"},
    {"number": "248", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/rhiPFOP.png"},
    {"number": "249", "hp": 55, "attack": "1d10", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/uvQXfOd.png"},
    {"number": "250", "hp": 50, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/tWfG1sy.png"},
    {"number": "251", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/tWfG1sy.png"},
    {"number": "252", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/hGCrD6H.png"},
    {"number": "253", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/8Jgw4sm.png"},
    {"number": "254", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/3mcoDKn.png"},
    {"number": "255", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/DXgUHO5.png"},
    {"number": "256", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/zrz0t2P.png"},
    {"number": "257", "hp": 50, "attack": "1d11", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/CSuw4zi.png"},
    {"number": "258", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/E0DNJtB.png"},
    {"number": "259", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/RIz1sop.png"},
    {"number": "260", "hp": 47, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/JBQ9pj6.png"},
    {"number": "261", "hp": 55, "attack": "1d10", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/2OKlVDw.png"},
    {"number": "262", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/brZQJ9T.png"},
    {"number": "263", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/OADxzAp.png"},
    {"number": "264", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/7dzDpqM.png"},
    {"number": "265", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/KebOqtw.png"},
    {"number": "266", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/93t9xtr.png"},
    {"number": "267", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/r09ru9Q.png"},
    {"number": "268", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/TJHPxZg.png"},
    {"number": "269", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/9Y7mis4.png"},
    {"number": "270", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/VzKABna.png"},
    {"number": "271", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/n86zSL0.png"},
    {"number": "272", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/YbvlSjU.png"},
    {"number": "273", "hp": 60, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/e6ySLAn.png"},
    {"number": "274", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/r7XyRiR.png"},
    {"number": "275", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/AqrRhDJ.png"},
    {"number": "276", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/6KBiXR9.png"},
    {"number": "277", "hp": 55, "attack": "1d11", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/qdMpJby.png"},
    {"number": "278", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/neUnR4n.png"},
    {"number": "279", "hp": 45, "attack": "1d8", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/m1LKM0u.png"},
    {"number": "280", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/1sP4lDD.png"},
    {"number": "281", "hp": 60, "attack": "1d9", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/2vvGq6c.png"},
    {"number": "282", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/syIyhd5.png"},
    {"number": "283", "hp": 47, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/XlSZsux.png"},
    {"number": "284", "hp": 45, "attack": "1d8", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/OKGciwt.png"},
    {"number": "285", "hp": 55, "attack": "1d11", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/wsABaAj.png"},
    {"number": "286", "hp": 50, "attack": "1d9", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/S3PprA5.png"},
    {"number": "287", "hp": 50, "attack": "1d9", "energy": 28, "defence": 0, "image_url": "https://i.imgur.com/uEli4s6.png"},
    {"number": "288", "hp": 45, "attack": "1d8", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/syIyhd5.png"},
    {"number": "289", "hp": 55, "attack": "1d10", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/fdKUrTm.png"},
    {"number": "290", "hp": 50, "attack": "1d9", "energy": 28, "defence": 1, "image_url": "https://i.imgur.com/ZuP5ck1.png"},
    {"number": "291", "hp": 50, "attack": "1d9", "energy": 30, "defence": 0, "image_url": "https://i.imgur.com/AnQCIIo.png"},
    {"number": "292", "hp": 45, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/yKFi9mH.png"},
    {"number": "293", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/LXzNesr.png"},
    {"number": "294", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/SyndByA.png"},
    {"number": "295", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/SOLwL3W.png"},
    {"number": "296", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/axiY8Bn.png"},
    {"number": "297", "hp": 50, "attack": "1d9", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/SKb9zfp.png"},
    {"number": "298", "hp": 55, "attack": "1d10", "energy": 25, "defence": 1, "image_url": "https://i.imgur.com/4TAevPA.png"},
    {"number": "299", "hp": 55, "attack": "1d10", "energy": 25, "defence": 0, "image_url": "https://i.imgur.com/yAJYNgx.png"},
]

# Equipment and spell database with brief descriptions
equipments = [
    {"name": "War Axe", "type": "Weapon", "weight": 2, "description": "+2 atk, +10 max HP", "image_url": "https://i.imgur.com/hw6bnk9.png", "attack_bonus": 2, "hp_bonus": 10, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Agility Ring", "type": "Ring", "weight": 1, "description": "+1 atk", "image_url": "https://i.imgur.com/X8V3IQ1.png", "attack_bonus": 1, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Perfect Ruby", "type": "Equipment", "weight": 1, "description": "+5 max HP", "image_url": "https://i.imgur.com/5G6bfns.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Great Shield", "type": "Shield", "weight": 2, "description": "+2 def", "image_url": "https://i.imgur.com/ofXdGx5.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 2, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Absorb", "type": "Spell", "weight": 2, "description": "deal 10 dmg, restore 10 HP, cost 10 energy", "image_url": "https://i.imgur.com/LqSkUnd.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Angel of Fire", "type": "Spell", "weight": 2, "description": "deal 12 dmg + 10/turn (2 turns), cost 15 energy", "image_url": "https://i.imgur.com/JwkktN4.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Divinity", "type": "Spell", "weight": 2, "description": "+3 HP/turn, +3 energy/turn, cost 10 energy", "image_url": "https://i.imgur.com/pR75h4e.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Infinity Aura", "type": "Spell", "weight": 2, "description": "+2 HP/turn, +2 energy/turn, cost 10 energy", "image_url": "https://i.imgur.com/8IcZM2t.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Blood Ring", "type": "Ring", "weight": 2, "description": "Recover 2 HP/turn", "image_url": "https://i.imgur.com/K4OLRPF.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 2, "energy_per_turn": 0},
    {"name": "Frost Ring", "type": "Ring", "weight": 1, "description": "Add 3 energy, +1 Energy/turn", "image_url": "https://i.imgur.com/Fd3NweD.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 3, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 1},
    {"name": "IS Rune", "type": "Equipment", "weight": 1, "description": "Add 1 attack", "image_url": "https://i.imgur.com/7tmZkb1.png", "attack_bonus": 1, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Val Rune", "type": "Equipment", "weight": 1, "description": "Add 5 Max HP and 5 energy", "image_url": "https://i.imgur.com/eHSmTsf.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 5, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Long Sword", "type": "Weapon", "weight": 2, "description": "Add 4 attack", "image_url": "https://i.imgur.com/ooPoD2o.png", "attack_bonus": 4, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Combat Shield", "type": "Shield", "weight": 2, "description": "Add 2 defence and 5 Max HP", "image_url": "https://i.imgur.com/sRaWGo6.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 2, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ice Bolt", "type": "Spell", "weight": 1, "description": "Deal 5 damage, disable spells 1 turn", "image_url": "https://i.imgur.com/OQ181Ri.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 7, "damage": 5, "disable_spell_turns": 1},
    {"name": "Shadow Ball", "type": "Spell", "weight": 2, "description": "Deal 10 damage", "image_url": "https://i.imgur.com/3kT9dho.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 8, "damage": 10},
    {"name": "Soul Drain", "type": "Spell", "weight": 2, "description": "Drain 15 energy, restore 15 energy", "image_url": "https://i.imgur.com/ARCEZpN.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "energy_drain": 15, "energy_restore": 15, "energy_cost": 0},
    {"name": "He Potion", "type": "Spell", "weight": 1, "description": "Restore 5 HP and 5 Energy", "image_url": "https://i.imgur.com/wdIpw6R.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 5, "energy_restore": 5},
    {"name": "Ring of Strength", "type": "Ring", "weight": 1, "description": "Add 1 attack and 5 Max HP", "image_url": "https://i.imgur.com/4D1VuO8.png", "attack_bonus": 1, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Shield of Light", "type": "Shield", "weight": 2, "description": "Add 1 defence and 10 energy", "image_url": "https://i.imgur.com/1vIot1j.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 10, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Healing Spring", "type": "Spell", "weight": 2, "description": "Restore 7 HP, +7 HP for 2 turns", "image_url": "https://i.imgur.com/0yNvYtE.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 8, "heal": 7, "heal_per_turn": 7, "heal_turns": 2},
    {"name": "Potion of Life", "type": "Spell", "weight": 1, "description": "Restore 10 HP and 5 Energy", "image_url": "https://i.imgur.com/VCXiG4L.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 10, "energy_restore": 5},
    {"name": "Book of Life", "type": "Equipment", "weight": 1, "description": "Add 1 defence, restore 1 HP/turn", "image_url": "https://i.imgur.com/7Sz100g.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 1, "energy_per_turn": 0},
    {"name": "Wooden Shield", "type": "Shield", "weight": 1, "description": "Add 1 defence", "image_url": "https://i.imgur.com/FxQwojj.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Great Staff", "type": "Weapon", "weight": 2, "description": "Add 2 attack and 10 Energy", "image_url": "https://i.imgur.com/5qtys84.png", "attack_bonus": 2, "hp_bonus": 0, "energy_bonus": 10, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Energy Ring", "type": "Ring", "weight": 1, "description": "Recover 1 Energy/turn", "image_url": "https://i.imgur.com/MNh3H6S.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Book of Strength", "type": "Equipment", "weight": 2, "description": "Add 1 attack and 3 max HP", "image_url": "https://i.imgur.com/gxc0S4q.png", "attack_bonus": 1, "hp_bonus": 3, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ruby", "type": "Equipment", "weight": 1, "description": "Add 3 max HP", "image_url": "https://i.imgur.com/DGrgZjw.png", "attack_bonus": 0, "hp_bonus": 3, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Red Ring", "type": "Ring", "weight": 1, "description": "Recover 1 HP/turn", "image_url": "https://i.imgur.com/Dvm5JQy.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 1, "energy_per_turn": 0},
    {"name": "Zil Rune", "type": "Equipment", "weight": 1, "description": "Add 1 defence", "image_url": "https://i.imgur.com/WvImfXp.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Eth Rune", "type": "Equipment", "weight": 2, "description": "Add 5 max HP and 5 Energy", "image_url": "https://i.imgur.com/oPluu0U.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 5, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Sapphire", "type": "Equipment", "weight": 1, "description": "Add 3 Energy", "image_url": "https://i.imgur.com/HapxRk7.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 3, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ring of Diversity", "type": "Ring", "weight": 2, "description": "Add 10 max HP and 10 Energy", "image_url": "https://i.imgur.com/HIA8uNl.png", "attack_bonus": 0, "hp_bonus": 10, "energy_bonus": 10, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Perfect Sapphire", "type": "Equipment", "weight": 1, "description": "Add 5 Energy", "image_url": "https://i.imgur.com/Wd10PiN.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 5, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Zakarul's Axe", "type": "Weapon", "weight": 3, "description": "Add 3 attack and 15 max HP", "image_url": "https://i.imgur.com/OVrDnMI.png", "attack_bonus": 3, "hp_bonus": 15, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ort Rune", "type": "Equipment", "weight": 2, "description": "Add 2 Attack, 1 defence, 5 max HP and 5 Energy", "image_url": "https://i.imgur.com/K5mE0N4.png", "attack_bonus": 2, "hp_bonus": 5, "energy_bonus": 5, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ancient Shield", "type": "Shield", "weight": 3, "description": "Add 3 defence and 10 max HP", "image_url": "https://i.imgur.com/7l87Nhq.png", "attack_bonus": 0, "hp_bonus": 10, "energy_bonus": 0, "defence_bonus": 3, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Crystal Shield", "type": "Shield", "weight": 3, "description": "Add 2 defence, 5 max HP and 10 Energy", "image_url": "https://i.imgur.com/CC60E7N.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 10, "defence_bonus": 2, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Wooden Staff", "type": "Weapon", "weight": 1, "description": "Add 1 attack and 5 Energy", "image_url": "https://i.imgur.com/AF6jZO9.png", "attack_bonus": 1, "hp_bonus": 0, "energy_bonus": 5, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Large Shield", "type": "Shield", "weight": 2, "description": "Add 1 defence and 5 max HP", "image_url": "https://i.imgur.com/JgeetzM.png", "attack_bonus": 0, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Ancient Staff", "type": "Weapon", "weight": 3, "description": "Add 2 attack, 15 Energy and 5 max HP", "image_url": "https://i.imgur.com/MwccZY3.png", "attack_bonus": 2, "hp_bonus": 5, "energy_bonus": 15, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Small Shield", "type": "Shield", "weight": 2, "description": "Add 1 defence and 5 Energy", "image_url": "https://i.imgur.com/ZzyGisw.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 5, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Small Sword", "type": "Weapon", "weight": 1, "description": "Add 2 attack", "image_url": "https://i.imgur.com/SuiR3Nv.png", "attack_bonus": 2, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Crystal Sword", "type": "Weapon", "weight": 3, "description": "Add 5 attack and drain 2 Energy/turn to opponent", "image_url": "https://i.imgur.com/NM5mQMf.png", "attack_bonus": 5, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_drain": 2},
    {"name": "Desenchant", "type": "Spell", "weight": 2, "description": "Reduce the Attack damage of the opponent by 2", "image_url": "https://i.imgur.com/QrR5piR.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 7, "attack_reduction": 2},
    {"name": "Energy Drain", "type": "Spell", "weight": 1, "description": "Drain 5 energy and restore 5 energy", "image_url": "https://i.imgur.com/Qke2clo.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "energy_drain": 5, "energy_restore": 5},
    {"name": "Energy Potion", "type": "Spell", "weight": 1, "description": "Restores 5 Energy", "image_url": "https://i.imgur.com/lRYyp4m.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "energy_restore": 5},
    {"name": "Fountain", "type": "Spell", "weight": 1, "description": "Restores 10 HP", "image_url": "https://i.imgur.com/dkSEcGO.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 5, "heal": 10},
    {"name": "Holy Dice", "type": "Spell", "weight": 1, "description": "Roll 1d12 and restore this value in HP to your little monster", "image_url": "https://i.imgur.com/kkoJK1q.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 6, "heal_dice": "1d12"},
    {"name": "Dagger Strike", "type": "Spell", "weight": 2, "description": "Throw a fire dagger, Roll 1d20 and do this value in damage to the Enemy little monster", "image_url": "https://i.imgur.com/lvcEluN.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "damage_dice": "1d20"},
    {"name": "Holy Heal", "type": "Spell", "weight": 2, "description": "Restores 30 HP", "image_url": "https://i.imgur.com/M3WKasG.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "heal": 30},
    {"name": "HP Potion", "type": "Spell", "weight": 1, "description": "Restores 5 HP", "image_url": "https://i.imgur.com/y6eiQYZ.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 5},
    {"name": "Energy Aura", "type": "Spell", "weight": 2, "description": "Restores 2 Energy every turn", "image_url": "https://i.imgur.com/OppRfML.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 2, "energy_cost": 6},
    {"name": "Immolate", "type": "Spell", "weight": 1, "description": "Deal 3 damages. The opponent is burn for 2 turns (2 damages per turn)", "image_url": "https://i.imgur.com/Des3WpS.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 7, "damage": 3, "burn_damage": 2, "burn_turns": 2},
    {"name": "Lightning Bolt", "type": "Spell", "weight": 1, "description": "Throw a lightning bolt that do 6 damages to the enemy little monster", "image_url": "https://i.imgur.com/ILnZ2OR.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 5, "damage": 6},
    {"name": "Fire Ball", "type": "Spell", "weight": 2, "description": "Deal 10 damages and burn enemy for 2 turns (3 damages per turn)", "image_url": "https://i.imgur.com/8ZMnTVO.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "damage": 10, "burn_damage": 3, "burn_turns": 2},
    {"name": "Gem of Force", "type": "Equipment", "weight": 0, "description": "Add 1 attack", "image_url": "https://i.imgur.com/kF3Eqxc.png", "attack_bonus": 1, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 0},
    {"name": "Gem of Life", "type": "Equipment", "weight": 0, "description": "Add 2 max Hp and 3 Energy", "image_url": "https://i.imgur.com/KmbfMRR.png", "attack_bonus": 0, "hp_bonus": 2, "energy_bonus": 3, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 0},
    {"name": "Restoration", "type": "Spell", "weight": 1, "description": "Restores 10 Energy to your little monster.", "image_url": "https://i.imgur.com/7h5hlkN.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 0, "heal": 0, "energy_restore": 10, "energy_cost": 0},
    {"name": "Secret Soul", "type": "Spell", "weight": 1, "description": "Roll 1 dice 10 and do this value in damage to the Enemy, cost 6.", "image_url": "https://i.imgur.com/5e2kSlB.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 6, "heal": 0, "damage_dice": "1d10"},
    {"name": "Shield Breaker", "type": "Spell", "weight": 1, "description": "Reduce the defence of the opponent by 1, cost 7.", "image_url": "https://i.imgur.com/1B5khvZ.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 4, "heal": 0, "defence_reduction": 1},
    {"name": "Nova", "type": "Spell", "weight": 2, "description": "Deal 10 damages. cost 10 energy The opponent can’t use spell next turn.", "image_url": "https://i.imgur.com/IDeABIf.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "heal": 0, "damage": 10, "disable_spell_turns": 1},
    {"name": "Shield of Life", "type": "Spell", "weight": 2, "description": "Restores 2 Hp every turn, cost 6 energy.", "image_url": "https://i.imgur.com/zHGh9hi.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 6, "heal": 0},
    {"name": "Axe", "type": "Weapon", "weight": 1, "description": "Add 1 attack damage and 5 max HP to the player little monsters", "image_url": "https://i.imgur.com/WeceUCU.png", "attack_bonus": 1, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Acid Bomb", "type": "Spell", "weight": 1, "description": "Throw a bomb of acid that do 5 damages and poison enemy for 2 turns (5 damages per turn)", "image_url": "https://i.imgur.com/ACH0eNw.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10, "damage": 5, "burn_damage": 5, "burn_turns": 2},
    {"name": "Immunity", "type": "Spell", "weight": 2, "description": "Your little monster is immune to move and spell damages next opponent turn.", "image_url": "https://i.imgur.com/mg8Ljtz.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 10},
    {"name": "Meteor", "type": "Spell", "weight": 2, "description": "Meteor rain causing 20 damages and burn 10 energy to the enemy little monster", "image_url": "https://i.imgur.com/mEg6fPC.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "energy_cost": 20, "damage": 20, "energy_drain": 10},
    {"name": "Red Flower", "type": "Equipment", "weight": 0, "description": "Add 1 HP/turn to Blood Ring", "image_url": "https://i.imgur.com/redflower.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 1, "energy_per_turn": 0},
    {"name": "Book of Aeralis", "type": "Equipment", "weight": 1, "description": "Recover 2 Energy every turn and Cannot be frozen", "image_url": "https://i.imgur.com/6tKZpAe.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 2, "prevent_freeze": True},
    {"name": "The Lost Page", "type": "Equipment", "weight": 0, "description": "Combine with any Book and add 3 Energy and 3 max Hp", "image_url": "https://i.imgur.com/Bkws5SD.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "requires_book": True},
    {"name": "Mystic Pearl", "type": "Equipment", "weight": 0, "description": "Add 3 max Hp and 3 Energy to Ring of Diversity stats", "image_url": "https://i.imgur.com/Krq032W.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "requires_diversity": True},
    {"name": "Frost Rune", "type": "Equipment", "weight": 1, "description": "Add 3 Hp to the player little monsters. Cannot be frozen", "image_url": "https://i.imgur.com/UI07UTM.png", "attack_bonus": 0, "hp_bonus": 3, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "prevent_freeze": True},
    {"name": "Jewel of Light", "type": "Equipment", "weight": 0, "description": "Reduce your Deck weight by 1", "image_url": "https://i.imgur.com/jo4PmlY.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "weight_reduction": 1},
    {"name": "Moonaxe", "type": "Weapon", "weight": 2, "description": "Add 1 attack damage, 5 max Hp and 1 defence to the player little monsters", "image_url": "https://i.imgur.com/WhrdPV1.png", "attack_bonus": 1, "hp_bonus": 5, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Deathwhirl", "type": "Weapon", "weight": 2, "description": "Add 2 attack damage, 10 max Hp and 1 defence to the player little monsters", "image_url": "https://i.imgur.com/hYGgB8B.png", "attack_bonus": 2, "hp_bonus": 10, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Light Staff", "type": "Weapon", "weight": 2, "description": "Add 10 Energy to the player little monsters and recover 1 Energy every turn", "image_url": "https://i.imgur.com/RKrX792.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 10, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 1},
    {"name": "Arcane Whisper", "type": "Weapon", "weight": 2, "description": "Add 2 attack damage, 15 energy and recover 1 energy every turn", "image_url": "https://i.imgur.com/j5yWywM.png", "attack_bonus": 2, "hp_bonus": 0, "energy_bonus": 15, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 1},
    {"name": "Emerald Warden", "type": "Weapon", "weight": 2, "description": "Add 3 attack damage, 15 energy and recover 2 energy every turn", "image_url": "https://i.imgur.com/1YKdWAv.png", "attack_bonus": 3, "hp_bonus": 0, "energy_bonus": 15, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 2},
    {"name": "Dwarf Sword", "type": "Weapon", "weight": 2, "description": "Add 2 attack damage to the player little monsters and recover 1 Hp every turn", "image_url": "https://i.imgur.com/cG4KtaC.png", "attack_bonus": 2, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 1, "energy_per_turn": 0},
    {"name": "Ez'Nir Rune", "type": "Equipment", "weight": 1, "description": "Add 2 Attack to the player little monsters", "image_url": "https://i.imgur.com/TlORnlY.png", "attack_bonus": 2, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Earthward", "type": "Equipment", "weight": 1, "description": "Add 1 defence and 12 max Hp to the player little monsters", "image_url": "https://i.imgur.com/Xj8FD4k.png", "attack_bonus": 0, "hp_bonus": 12, "energy_bonus": 0, "defence_bonus": 1, "hp_per_turn": 0, "energy_per_turn": 0},
    {"name": "Crystal Powder", "type": "Equipment", "weight": 0, "description": "Combine Perfect Ruby or Perfect Sapphire to Combat Shield or Shield of Light, Reduce weight by 1", "image_url": "https://i.imgur.com/TsgPw8q.png", "attack_bonus": 0, "hp_bonus": 0, "energy_bonus": 0, "defence_bonus": 0, "hp_per_turn": 0, "energy_per_turn": 0, "weight_reducer": True},]

# Points pour équipements et sorts
equipment_points = {
    "War Axe": 10,
    "Agility Ring": 5,
    "Perfect Ruby": 5,
    "Great Shield": 10,
    "Absorb": 15,
    "Angel of Fire": 20,
    "Divinity": 15,
    "Infinity Aura": 15,
    "Blood Ring": 10,
    "Frost Ring": 10,
    "IS Rune": 5,
    "Val Rune": 10,
    "Long Sword": 10,
    "Combat Shield": 10,
    "Ice Bolt": 10,
    "Shadow Ball": 15,
    "Soul Drain": 15,
    "He Potion": 5,
    "Ring of Strength": 10,
    "Shield of Light": 10,
    "Healing Spring": 15,
    "Potion of Life": 10,
    "Book of Life": 10,
    "Wooden Shield": 5,
    "Great Staff": 10,
    "Energy Ring": 5,
    "Book of Strength": 10,
    "Ruby": 5,
    "Red Ring": 5,
    "Zil Rune": 5,
    "Eth Rune": 10,
    "Sapphire": 5,
}

# Points pour équipements et sorts
equipment_points = {
    "Crystal Sword": 15,
    "Meteor": 12,
    "Zakarul's Axe": 12,
    "Ancient Shield": 9,
    "Crystal Shield": 9,
    "Holy Heal": 15,
    "Ring of Diversity": 12,
    "Ancient Staff": 9,
    "Ort Rune": 12,
    "Immunity": 5,
    "Fire Ball": 8,
    "Angel of Fire": 15,
    "Long Sword": 10,
    "Nova": 8,
    "Healing Spring": 10,
    "Absorb": 10,
    "Combat Shield": 8,
    "Great Staff": 8,
    "Divinity": 10,
    "Infinity Aura": 8,
    "Blood Ring": 8,
    "Frost Ring": 8,
    "Acid Bomb": 8,
    "Soul Drain": 12,
    "Great Shield": 4,
    "Potion of Life": 10,
    "Shield of Light": 6,
    "Book of Strength": 2,
    "Eth Rune": 3,
    "Val Rune": 10,
    "Ring of Strength": 6,
    "Wooden Shield": 2,
    "Small Sword": 2,
    "War Axe": 9,
    "Wooden Staff": 2,
    "Large Shield": 3,
    "Small Shield": 3,
    "Book of Life": 12,
    "Shadow Ball": 5,
    "Fountain": 4,
    "Desenchant": 3,
    "Ice Bolt": 5,
    "Mystic Pearl": 7,
    "Ez'Nir Rune": 9,
    "Earthward": 7,
    "Crystal Powder": 3,
    "Book of Aeralis": 8,
    "Perfect Sapphire": 3,
    "Perfect Ruby": 3,
    "Sapphire": 1,
    "Ruby": 1,
    "Agility Ring": 2,
    "Red Ring": 3,
    "Zil Rune": 2,
    "IS Rune": 3,
    "Energy Ring": 3,
    "Dagger Strike": 8,
    "Holy Dice": 3,
    "Secret Soul": 3,
    "Lightning Bolt": 3,
    "Immolate": 2,
    "Energy Aura": 2,
    "He Potion": 3,
    "HP Potion": 1,
    "Energy Potion": 1,
    "Energy Drain": 2,
    "Restoration": 2,
    "Shield Breaker": 1,
    "Shield of Life": 2,
    "Gem of Force": 8,
    "Gem of Life": 8,
    "Red Flower": 4,
    "The Lost Page": 4,
}

# Chemin du fichier JSON pour la persistance
DATA_FILE = "players_data.json"

# Dictionnaires pour stocker les choix des joueurs et leurs stats
player_monsters = {}
player_decks = {}
player_stats = {}

def load_player_data():
    """Charge les données des joueurs depuis le fichier JSON."""
    global player_monsters, player_decks, player_stats
    if os.path.exists(DATA_FILE):
        try:
            with open(DATA_FILE, 'r') as f:
                data = json.load(f)
                # Réinitialiser les dictionnaires avant de charger
                player_monsters.clear()
                player_decks.clear()
                player_stats.clear()
                # Charger les données en s'assurant de convertir les clés en entiers
                player_monsters.update({int(k): v for k, v in data.get("monsters", {}).items()})
                player_decks.update({int(k): v for k, v in data.get("decks", {}).items()})
                player_stats.update({int(k): v for k, v in data.get("stats", {}).items()})
                # Initialiser le champ "attributes" pour chaque joueur si absent
                for player_id in player_stats:
                    if "attributes" not in player_stats[player_id]:
                        player_stats[player_id]["attributes"] = {
                            "available": 0,
                            "damage": 0,
                            "hp": 0,
                            "energy": 0,
                            "defence": 0
                        }
                    if "gems" not in player_stats[player_id]:
                        player_stats[player_id]["gems"] = 0
                    if "last_gem_gain" not in player_stats[player_id]:
                        player_stats[player_id]["last_gem_gain"] = None
                logger.info(f"Données chargées depuis {DATA_FILE}:")
                logger.info(f"Monstres: {player_monsters}")
                logger.info(f"Decks: {player_decks}")
                logger.info(f"Stats: {player_stats}")
        except json.JSONDecodeError as e:
            logger.error(f"Erreur de décodage JSON dans {DATA_FILE}: {e}. Réinitialisation des données.")
            player_monsters.clear()
            player_decks.clear()
            player_stats.clear()
            save_player_data()
    else:
        logger.info(f"Le fichier {DATA_FILE} n'existe pas. Création avec des données vides.")
        player_monsters.clear()
        player_decks.clear()
        player_stats.clear()
        save_player_data()

def save_player_data():
    """Sauvegarde les données des joueurs dans le fichier JSON."""
    data = {
        "monsters": player_monsters,
        "decks": player_decks,
        "stats": player_stats
    }
    try:
        with open(DATA_FILE, 'w') as f:
            json.dump(data, f, indent=4)
        logger.info(f"Données sauvegardées dans {DATA_FILE}:")
        logger.info(f"Monstres: {player_monsters}")
        logger.info(f"Decks: {player_decks}")
        logger.info(f"Stats: {player_stats}")
    except Exception as e:
        logger.error(f"Erreur lors de la sauvegarde dans {DATA_FILE}: {e}")

def get_monster_points(monster_number):
    """Retourne les points d'un monstre en fonction de ses HP."""
    for monster in monsters:
        if monster["number"] == monster_number:
            hp = monster["hp"]
            if hp <= 45:
                return 1
            elif hp <= 52:
                return 7
            else:
                return 20
    return 0  # En cas d'erreur (monstre non trouvé)

def validate_player_decks():
    """Vérifie et corrige les noms des équipements dans les decks des joueurs."""
    equipment_names = {item["name"] for item in equipments}
    
    for player_id, deck in player_decks.items():
        for i, item in enumerate(deck):
            if item["name"] not in equipment_names:
                # Essayer de trouver un équipement correspondant
                for equip in equipments:
                    if equip["name"].lower() == item["name"].lower():
                        logger.info(f"Correcting equipment name from '{item['name']}' to '{equip['name']}' for player {player_id}")
                        deck[i] = equip
                        break
    
    # Sauvegarder les modifications
    save_player_data()

# Charger les données au démarrage
load_player_data()
validate_player_decks()
