# combat.py
import discord
from discord import Embed
from discord.ext import tasks
import asyncio
import random
import logging
import math
import time
from datetime import datetime, timezone
from config import bot
from data import monsters, equipments, player_monsters, player_decks, player_stats, save_player_data, equipment_points, get_monster_points
from utils import calculate_bonuses, calculate_total_weight
from user_commands import calculate_season_level_and_exp
from leaderboard import update_leaderboard

# Configuration des logs
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

SPAWN_CHANNEL_ID = 1372384339265847308  # Canal où les monstres apparaissent

def roll_d20():
    return random.randint(1, 20)

def roll_attack_die(attack_str):
    num, sides = attack_str.split('d')
    return random.randint(1, int(sides))

def roll_dice(dice_str):
    num, sides = dice_str.split('d')
    return random.randint(1, int(sides))

def generate_random_opponent():
    monster = random.choice(monsters)
    available_equipments = equipments.copy()
    random.shuffle(available_equipments)
    opponent_deck = []
    total_weight = 0
    type_counts = {"Weapon": 0, "Shield": 0, "Ring": 0, "Equipment": 0}

    for item in available_equipments:
        if total_weight + item["weight"] > 10:
            continue
        if item["type"] in type_counts:
            if item["type"] == "Weapon" and type_counts["Weapon"] >= 1:
                continue
            if item["type"] == "Shield" and type_counts["Shield"] >= 1:
                continue
            if item["type"] == "Ring" and type_counts["Ring"] >= 2:
                continue
            if item["type"] == "Equipment" and type_counts["Equipment"] >= 3:
                continue
            type_counts[item["type"]] += 1
        opponent_deck.append(item)
        total_weight += item["weight"]
        if total_weight >= 10:
            break

    # Générer aléatoirement entre 0 et 15 points d'attributs
    total_attribute_points = random.randint(0, 15)
    # Répartir les points aléatoirement entre damage, hp, energy, defence
    attributes = {"damage": 0, "hp": 0, "energy": 0, "defence": 0}
    for _ in range(total_attribute_points):
        attr = random.choice(["damage", "hp", "energy", "defence"])
        attributes[attr] += 1

    return {
        "monster": monster,
        "deck": opponent_deck,
        "attributes": attributes
    }

@tasks.loop(minutes=5)
async def spawn_random_monster():
    channel = bot.get_channel(SPAWN_CHANNEL_ID)
    if not channel:
        logger.error(f"Error: Channel with ID {SPAWN_CHANNEL_ID} not found.")
        return

    opponent = generate_random_opponent()
    monster = opponent["monster"]
    deck = opponent["deck"]
    attributes = opponent["attributes"]
    attack_bonus, hp_bonus, energy_bonus, defence_bonus, hp_per_turn, energy_per_turn, prevent_freeze, last_turn_when_dead = calculate_bonuses(deck)

    # Appliquer les bonus d'attributs
    modified_attack = monster["attack"]
    modified_hp = monster["hp"] + hp_bonus + (attributes["hp"] * 4)
    modified_energy = monster["energy"] + energy_bonus + (attributes["energy"] * 3)
    modified_defence = monster["defence"] + defence_bonus + attributes["defence"]
    total_weight = calculate_total_weight(deck)

    # Ajouter le bonus d'attribut damage à attack_bonus
    attack_bonus += attributes["damage"]

    # Calculer le total des points d'attributs
    total_attribute_points = sum(attributes.values())
    # Calculer le niveau supplémentaire (+1 par 2 points)
    attribute_level_bonus = total_attribute_points // 2

    # Calculer les points d'expérience
    deck_points = sum(equipment_points.get(item["name"], 0) for item in deck)
    monster_points = get_monster_points(monster["number"])
    points_earned = deck_points + monster_points + (total_attribute_points * 3)
    level = round(points_earned / 4) + attributes["defence"]

    # Log des attributs générés
    logger.info(f"Generated wild monster #{monster['number']} with attributes: {attributes}, total points: {total_attribute_points}, level bonus: {attribute_level_bonus}, extra Exp: {total_attribute_points * 4}")

    embed = Embed(title=f"A Wild Monster Lv: {level} Appears!", color=discord.Color.red())
    embed.set_thumbnail(url=monster["image_url"])
    stats = (
        f"**Damage:** {modified_attack} + {attack_bonus}\n"
        f"**HP:** {modified_hp}/{modified_hp} + {hp_per_turn}\n"
        f"**Energy:** {modified_energy} + {energy_per_turn}\n"
        f"**Defense:** {modified_defence}\n"
    )
    embed.add_field(name="Stats", value=stats, inline=False)
    embed.add_field(name="\u200b", value="\u200b", inline=False)
    equipment_str = "\n".join(f"- {item['name']} ({item['type']})" for item in deck if item["type"] != "Spell") or "None"
    embed.add_field(name="Equipment", value=equipment_str, inline=False)
    spell_str = "\n".join(f"- {item['name']}" for item in deck if item["type"] == "Spell") or "None"
    embed.add_field(name="Spells", value=spell_str, inline=False)
    embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)
    embed.add_field(name="\u200b", value="\u200b", inline=False)
    attributes_str = (
        f"Damage: +{attributes['damage']}\n"
        f"HP: +{attributes['hp'] * 4}\n"
        f"Energy: +{attributes['energy'] * 3}\n"
        f"Defence: +{attributes['defence']}"
    )
    embed.add_field(name="Attributes", value=attributes_str, inline=False)
    embed.set_footer(text="React with ✅ to challenge this monster! (Disappears in 4 minutes)")

    message = None
    try:
        message = await channel.send(embed=embed)
        await message.add_reaction("✅")
    except discord.errors.Forbidden:
        logger.error(f"Error: Bot lacks permissions to send messages or add reactions in channel {SPAWN_CHANNEL_ID}.")
        return
    except Exception as e:
        logger.error(f"Unexpected error while sending monster spawn message: {e}")
        return

    def check(reaction, user):
        return reaction.message.id == message.id and str(reaction.emoji) == "✅" and user != bot.user

    fight_channel = None
    start_message = None
    try:
        end_time = asyncio.get_event_loop().time() + 240.0
        while asyncio.get_event_loop().time() < end_time:
            try:
                reaction, challenger = await bot.wait_for("reaction_add", timeout=end_time - asyncio.get_event_loop().time(), check=check)
                if challenger.id not in player_monsters:
                    await channel.send(f"{challenger.mention}, you need to equip a monster! Contact an admin with /monster.")
                    continue
                else:
                    await message.delete()
                    break
            except asyncio.TimeoutError:
                await message.delete()
                flee_message = await channel.send("The wild monster has fled after no one challenged it!")
                await asyncio.sleep(60)
                await flee_message.delete()
                return

        guild = channel.guild
        bot_member = guild.get_member(bot.user.id)
        if not bot_member:
            logger.error("Bot member not found in guild.")
            await channel.send("Error: Bot cannot find itself in the server. Please contact an admin.")
            return
        if not guild.me.guild_permissions.manage_channels:
            logger.error("Bot lacks 'Manage Channels' permission in guild.")
            await channel.send("Error: Bot lacks permission to create or delete channels. Please contact an admin.")
            return

        battlefield_number = 1
        while True:
            battlefield_name = f"battlefield-{battlefield_number}"
            if not any(ch.name == battlefield_name for ch in guild.text_channels):
                break
            battlefield_number += 1

        fight_channel = await guild.create_text_channel(battlefield_name)
        logger.info(f"Created battle channel {battlefield_name}.")
        start_message = await channel.send(f"{challenger.mention}, your fight against Monster #{monster['number']} has started in {fight_channel.mention}!")

        player_id = challenger.id
        player_monster = player_monsters[player_id]
        player_deck = player_decks.get(player_id, [])
        player_stat = player_stats.get(player_id, {"points": 0, "pvp_wins": 0, "pvp_losses": 0, "pvp_points": 0})

        if "attributes" not in player_stat:
            player_stat["attributes"] = {
                "available": 0,
                "damage": 0,
                "hp": 0,
                "energy": 0,
                "defence": 0
            }
            player_stats[player_id] = player_stat
            save_player_data()

        p_attack_bonus, p_hp_bonus, p_energy_bonus, p_defence_bonus, p_hp_per_turn, p_energy_per_turn, p_prevent_freeze, p_last_turn_when_dead = calculate_bonuses(player_deck)

        p_attack_bonus_with_attributes = math.ceil(p_attack_bonus + player_stat["attributes"]["damage"] * 0.75)
        p_hp_with_attributes = math.ceil(player_monster["hp"] + p_hp_bonus + player_stat["attributes"]["hp"] * 2.5)
        p_energy_with_attributes = math.ceil(player_monster["energy"] + p_energy_bonus + player_stat["attributes"]["energy"] * 2.0)
        p_defence_with_attributes = math.ceil(player_monster["defence"] + p_defence_bonus + player_stat["attributes"]["defence"] * 0.5)

        player = {
            "hp": p_hp_with_attributes,
            "max_hp": p_hp_with_attributes,
            "energy": p_energy_with_attributes,
            "attack": player_monster["attack"],
            "attack_bonus": p_attack_bonus_with_attributes,
            "defence": p_defence_with_attributes,
            "deck": player_deck,
            "spell_counts": {item["name"]: sum(1 for i in player_deck if i["name"] == item["name"] and i["type"] == "Spell") for item in player_deck if item["type"] == "Spell"},
            "used_spells": {},
            "angel_of_fire_turns": 0,
            "divinity_active": False,
            "healing_spring_turns": 0,
            "spells_disabled": 0,
            "status_effects": {},
            "energy_aura_active": False,
            "fire_ball_turns": 0,
            "immolate_turns": 0,
            "infinity_aura_active": False,
            "immune": False,
            "prevent_freeze": p_prevent_freeze,
            "last_turn_when_dead": p_last_turn_when_dead,
            "last_turn_used": False
        }
        enemy = {
            "hp": modified_hp,
            "max_hp": modified_hp,
            "energy": modified_energy,
            "attack": modified_attack,
            "attack_bonus": attack_bonus,
            "defence": modified_defence,
            "deck": deck,
            "spell_counts": {item["name"]: sum(1 for i in deck if i["name"] == item["name"] and i["type"] == "Spell") for item in deck if item["type"] == "Spell"},
            "used_spells": {},
            "angel_of_fire_turns": 0,
            "divinity_active": False,
            "healing_spring_turns": 0,
            "spells_disabled": 0,
            "status_effects": {},
            "energy_aura_active": False,
            "fire_ball_turns": 0,
            "immolate_turns": 0,
            "infinity_aura_active": False,
            "immune": False,
            "prevent_freeze": prevent_freeze,
            "last_turn_when_dead": False,
            "last_turn_used": False
        }

        player["energy"] += sum(1 for item in player["deck"] if item["name"] == "Frost Ring") * 3
        enemy["energy"] += sum(1 for item in enemy["deck"] if item["name"] == "Frost Ring") * 3

        player_roll = roll_d20()
        enemy_roll = roll_d20()
        await fight_channel.send(f"{challenger.mention} rolled a **{player_roll}**!\nMonster #{monster['number']} rolled a **{enemy_roll}**!")
        player_turn = player_roll >= enemy_roll
        await fight_channel.send(f"{'Player' if player_turn else 'Monster #' + str(monster['number'])} starts the battle!")

        while player["hp"] > 0 and enemy["hp"] > 0:
            # Appliquer les bonus HP et énergie par tour (via calculate_bonuses)
            player["hp"] = min(player["max_hp"], player["hp"] + p_hp_per_turn)
            enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + hp_per_turn)
            player["energy"] += p_energy_per_turn
            enemy["energy"] += energy_per_turn

            # Gestion des effets de statut (burn)
            if "burn" in player["status_effects"]:
                burn_damage = player["status_effects"]["burn"]["damage"]
                if not player["immune"]:
                    player["hp"] -= burn_damage
                else:
                    await fight_channel.send(f"{challenger.display_name}'s Immunity blocks the burn damage!")
                player["status_effects"]["burn"]["turns"] -= 1
                await fight_channel.send(f"{challenger.display_name}'s monster takes {burn_damage} burn damage!" if not player["immune"] else "")
                if player["status_effects"]["burn"]["turns"] <= 0:
                    del player["status_effects"]["burn"]
            if "burn" in enemy["status_effects"]:
                burn_damage = enemy["status_effects"]["burn"]["damage"]
                if not enemy["immune"]:
                    enemy["hp"] -= burn_damage
                else:
                    await fight_channel.send(f"Monster #{monster['number']}'s Immunity blocks the burn damage!")
                enemy["status_effects"]["burn"]["turns"] -= 1
                await fight_channel.send(f"Monster #{monster['number']} takes {burn_damage} burn damage!" if not enemy["immune"] else "")
                if enemy["status_effects"]["burn"]["turns"] <= 0:
                    del enemy["status_effects"]["burn"]

            if player_turn:
                hp_bonus_this_turn = 0
                e_energy_bonus_this_turn = 0
                energy_bonus_this_turn = 0
                extra_damage_this_turn = 0

                attack_roll = roll_attack_die(player["attack"])
                if any(item["name"] == "Crystal Sword" for item in player["deck"]):
                    enemy["energy"] = max(0, enemy["energy"] - 2)
                    await fight_channel.send(f"{challenger.display_name}'s Crystal Sword burns 2 energy from Monster #{monster['number']}!")

                if player["infinity_aura_active"]:
                    player["hp"] = min(player["max_hp"], player["hp"] + 2)
                    player["energy"] += 2
                    hp_bonus_this_turn += 2
                    energy_bonus_this_turn += 2
                if "shield_of_life" in player["status_effects"] and player["status_effects"]["shield_of_life"]["active"]:
                    player["hp"] = min(player["max_hp"], player["hp"] + player["status_effects"]["shield_of_life"]["hp_per_turn"])
                    hp_bonus_this_turn += player["status_effects"]["shield_of_life"]["hp_per_turn"]
                    await fight_channel.send(f"{challenger.display_name} gains {player['status_effects']['shield_of_life']['hp_per_turn']} HP from Shield of Life!")
                blood_ring_count = sum(1 for item in player["deck"] if item["name"] == "Blood Ring")
                player["hp"] = min(player["max_hp"], player["hp"] + blood_ring_count * 2)
                hp_bonus_this_turn += blood_ring_count * 2
                frost_ring_count = sum(1 for item in player["deck"] if item["name"] == "Frost Ring")
                player["energy"] += frost_ring_count * 1
                energy_bonus_this_turn += frost_ring_count * 1
                if any(item["name"] == "Energy Ring" for item in player["deck"]):
                    player["energy"] += 1
                    energy_bonus_this_turn += 1
                if player["energy_aura_active"]:
                    player["energy"] += 2
                    energy_bonus_this_turn += 2
                total_attack = attack_roll + player["attack_bonus"]
                if player["angel_of_fire_turns"] > 0:
                    total_attack += 10
                    extra_damage_this_turn += 10
                if player["fire_ball_turns"] > 0:
                    total_attack += 3
                    extra_damage_this_turn += 3
                if player["immolate_turns"] > 0:
                    total_attack += 2
                    extra_damage_this_turn += 2
                if "poison" in enemy["status_effects"]:
                    poison_damage = enemy["status_effects"]["poison"]["damage"]
                    total_attack += poison_damage
                    enemy["status_effects"]["poison"]["turns"] -= 1
                    if enemy["status_effects"]["poison"]["turns"] <= 0:
                        del enemy["status_effects"]["poison"]
                if player["divinity_active"]:
                    player["hp"] = min(player["max_hp"], player["hp"] + 3)
                    player["energy"] += 3
                    hp_bonus_this_turn += 3
                    energy_bonus_this_turn += 3
                if player["healing_spring_turns"] > 0:
                    player["hp"] = min(player["max_hp"], player["hp"] + 7)
                    hp_bonus_this_turn += 7

                if not enemy["immune"]:
                    enemy["hp"] -= max(0, total_attack - enemy["defence"])
                else:
                    await fight_channel.send(f"Monster #{monster['number']}'s Immunity blocks the damage!")

                show_spell_box = False
                player_spells = [item for item in player["deck"] if item["type"] == "Spell"]
                available_spells = []
                for spell in player_spells:
                    used_count = player["used_spells"].get(spell["name"], 0)
                    max_count = player["spell_counts"].get(spell["name"], 0)
                    if spell["name"] == "Absorb" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Absorb", "damage": 10, "energy_cost": 10, "heal": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Angel of Fire" and used_count < max_count and player["energy"] >= 15:
                        available_spells.append({"name": "Angel of Fire", "damage": 12, "energy_cost": 15, "dot": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Divinity" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Divinity", "heal_per_turn": 3, "energy_per_turn": 3, "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Ice Bolt" and used_count < max_count and player["energy"] >= 7:
                        available_spells.append({"name": "Ice Bolt", "damage": 5, "energy_cost": 7, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                    elif spell["name"] == "Shadow Ball" and used_count < max_count and player["energy"] >= 8:
                        available_spells.append({"name": "Shadow Ball", "damage": 10, "energy_cost": 8, "image_url": spell["image_url"]})
                    elif spell["name"] == "Soul Drain" and used_count < max_count and player["hp"] >= 10:
                        available_spells.append({"name": "Soul Drain", "energy_drain": 15, "energy_restore": 15, "hp_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "He Potion" and used_count < max_count and player["energy"] >= 0:
                        available_spells.append({"name": "He Potion", "heal": 5, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                    elif spell["name"] == "Potion of Life" and used_count < max_count and player["energy"] >= 0:
                        available_spells.append({"name": "Potion of Life", "heal": 10, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                    elif spell["name"] == "Healing Spring" and used_count < max_count and player["energy"] >= 8:
                        available_spells.append({"name": "Healing Spring", "heal": 7, "heal_per_turn": 7, "heal_turns": 2, "energy_cost": 8, "image_url": spell["image_url"]})
                    elif spell["name"] == "Desenchant" and used_count < max_count and player["energy"] >= 7:
                        available_spells.append({"name": "Desenchant", "attack_reduction": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                    elif spell["name"] == "Energy Drain" and used_count < max_count and player["hp"] >= 5:
                        available_spells.append({"name": "Energy Drain", "energy_drain": 5, "energy_restore": 5, "hp_cost": 5, "image_url": spell["image_url"]})
                    elif spell["name"] == "Energy Potion" and used_count < max_count and player["energy"] >= 0:
                        available_spells.append({"name": "Energy Potion", "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                    elif spell["name"] == "Fountain" and used_count < max_count and player["energy"] >= 5:
                        available_spells.append({"name": "Fountain", "heal": 10, "energy_cost": 5, "image_url": spell["image_url"]})
                    elif spell["name"] == "Holy Dice" and used_count < max_count and player["energy"] >= 6:
                        available_spells.append({"name": "Holy Dice", "heal_dice": "1d12", "energy_cost": 6, "image_url": spell["image_url"]})
                    elif spell["name"] == "Dagger Strike" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Dagger Strike", "damage_dice": "1d20", "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Holy Heal" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Holy Heal", "heal": 30, "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "HP Potion" and used_count < max_count and player["energy"] >= 0:
                        available_spells.append({"name": "HP Potion", "heal": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                    elif spell["name"] == "Energy Aura" and used_count < max_count and player["hp"] >= 6:
                        available_spells.append({"name": "Energy Aura", "energy_per_turn": 2, "hp_cost": 6, "image_url": spell["image_url"]})
                    elif spell["name"] == "Immolate" and used_count < max_count and player["energy"] >= 7:
                        available_spells.append({"name": "Immolate", "damage": 3, "extra_damage": 2, "extra_turns": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                    elif spell["name"] == "Lightning Bolt" and used_count < max_count and player["energy"] >= 5:
                        available_spells.append({"name": "Lightning Bolt", "damage": 6, "energy_cost": 5, "image_url": spell["image_url"]})
                    elif spell["name"] == "Fire Ball" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Fire Ball", "damage": 10, "extra_damage": 3, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Shield of Life" and used_count < max_count and player["energy"] >= 5:
                        available_spells.append({"name": "Shield of Life", "hp_per_turn": 2, "energy_cost": 5, "image_url": spell["image_url"]})
                    elif spell["name"] == "Nova" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Nova", "damage": 10, "energy_cost": 10, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                    elif spell["name"] == "Acid Bomb" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Acid Bomb", "damage": 5, "extra_damage": 5, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Restoration" and used_count < max_count and player["hp"] >= 5:
                        available_spells.append({"name": "Restoration", "energy_restore": 10, "hp_cost": 5, "image_url": spell["image_url"]})
                    elif spell["name"] == "Secret Soul" and used_count < max_count and player["energy"] >= 6:
                        available_spells.append({"name": "Secret Soul", "damage_dice": "1d10", "energy_cost": 6, "image_url": spell["image_url"]})
                    elif spell["name"] == "Shield Breaker" and used_count < max_count and player["energy"] >= 4:
                        available_spells.append({"name": "Shield Breaker", "defence_reduction": 1, "energy_cost": 4, "image_url": spell["image_url"]})
                    elif spell["name"] == "Infinity Aura" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Infinity Aura", "hp_per_turn": 2, "energy_per_turn": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                    elif spell["name"] == "Meteor" and used_count < max_count and player["energy"] >= 20:
                        available_spells.append({"name": "Meteor", "damage": 20, "energy_drain": 10, "energy_cost": 20, "image_url": spell["image_url"]})
                    elif spell["name"] == "Immunity" and used_count < max_count and player["energy"] >= 10:
                        available_spells.append({"name": "Immunity", "energy_cost": 10, "image_url": spell["image_url"]})

                if available_spells and player["spells_disabled"] == 0:
                    show_spell_box = True
                    spell_embed = Embed(title=f"{challenger.display_name}, it's your turn!", color=discord.Color.blue())
                    spell_embed.set_thumbnail(url=player_monster["image_url"])
                    spell_extra_damage = 0
                    if player["angel_of_fire_turns"] > 0:
                        spell_extra_damage += 10
                        player["angel_of_fire_turns"] -= 1
                    if player["fire_ball_turns"] > 0:
                        spell_extra_damage += 3
                        player["fire_ball_turns"] -= 1
                    if player["immolate_turns"] > 0:
                        spell_extra_damage += 2
                        player["immolate_turns"] -= 1
                    if "poison" in enemy["status_effects"]:
                        spell_extra_damage += enemy["status_effects"]["poison"]["damage"]
                        enemy["status_effects"]["poison"]["turns"] -= 1
                        if enemy["status_effects"]["poison"]["turns"] <= 0:
                            del enemy["status_effects"]["poison"]
                    hp_bonus_total = p_hp_per_turn + (3 if player["divinity_active"] else 0) + hp_bonus_this_turn
                    if player["healing_spring_turns"] > 0:
                        hp_bonus_total += 7
                    spell_embed.add_field(
                        name="Stats",
                        value=(
                            f"**Damage:** {attack_roll} + {player['attack_bonus']}{' +'+str(spell_extra_damage) if spell_extra_damage > 0 else ''}\n"
                            f"**HP:** {player['hp']}/{player['max_hp']} + {hp_bonus_total}\n"
                            f"**Energy:** {player['energy']} + {p_energy_per_turn + (3 if player['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                            f"**Defense:** {player['defence']}"
                        ),
                        inline=False
                    )
                    spell_embed.add_field(name="\u200b", value="\u200b", inline=False)
                    enemy_energy_modifier = energy_per_turn + (3 if enemy["divinity_active"] else 0) + e_energy_bonus_this_turn
                    if any(item["name"] == "Crystal Sword" for item in player["deck"]):
                        enemy_energy_modifier_str = f" +{enemy_energy_modifier} -2" if enemy_energy_modifier > 0 else " -2"
                    else:
                        enemy_energy_modifier_str = f" +{enemy_energy_modifier}" if enemy_energy_modifier > 0 else ""
                    spell_embed.add_field(
                        name=f"Monster #{monster['number']}",
                        value=f"**HP:** {enemy['hp']}/{enemy['max_hp']} + {hp_per_turn + (3 if enemy['divinity_active'] else 0)}\n"
                              f"**Energy:** {enemy['energy']}{enemy_energy_modifier_str}\n"
                              f"**Defense:** {enemy['defence']}",
                        inline=False
                    )
                    spell_embed.add_field(name="\u200b", value="\u200b", inline=False)
                    spell_text = "Choose a spell to use or skip with ❌:\n" + "\n".join(
                        [
                            f"{i+1}️⃣ - {s['name']}" +
                            (f" (Damage: {s['damage']}, Heal: {s['heal']}, Energy Cost: {s['energy_cost']})" if "heal" in s and "damage" in s else
                             f" (Damage: {s['damage']}, Energy Cost: {s['energy_cost']})" if "damage" in s and "heal" not in s and "extra_damage" not in s and "energy_drain" not in s else
                             f" (Damage: {s['damage']}, Energy Drain: {s['energy_drain']}, Energy Cost: {s['energy_cost']})" if "damage" in s and "energy_drain" in s else
                             f" (Heal: {s['heal']}, Energy Gain: {s['energy_restore']}, Energy Cost: {s['energy_cost']})" if "heal" in s and "energy_restore" in s else
                             f" (Heal/Turn: {s['heal_per_turn']}, Energy/Turn: {s['energy_per_turn']}, Energy Cost: {s['energy_cost']})" if "heal_per_turn" in s and "heal" not in s and "hp_per_turn" not in s else
                             f" (Heal: {s['heal']}, Heal/Turn: {s['heal_per_turn']}, Energy Cost: {s['energy_cost']})" if "heal_per_turn" in s and "heal" in s else
                             f" (HP/Turn: {s['hp_per_turn']}, Energy/Turn: {s['energy_per_turn']}, Energy Cost: {s['energy_cost']})" if "hp_per_turn" in s and "energy_per_turn" in s else
                             f" (HP/Turn: {s['hp_per_turn']}, Energy Cost: {s['energy_cost']})" if "hp_per_turn" in s and "energy_per_turn" not in s else
                             f" (Energy/Turn: {s['energy_per_turn']}, HP Cost: {s['hp_cost']})" if "energy_per_turn" in s and "hp_per_turn" not in s else
                             f" (Drain Energy: {s['energy_drain']}, Gain Energy: {s['energy_restore']}, HP Cost: {s['hp_cost']})" if "energy_drain" in s and "hp_cost" in s else
                             f" (Damage: {s['damage']}, Disable Spells: {s['disable_spell_turns']} turn, Energy Cost: {s['energy_cost']})" if "disable_spell_turns" in s else
                             f" (Reduce Attack: {s['attack_reduction']}, Energy Cost: {s['energy_cost']})" if "attack_reduction" in s else
                             f" (Reduce Defence: {s['defence_reduction']}, Energy Cost: {s['energy_cost']})" if "defence_reduction" in s else
                             f" (Energy Gain: {s['energy_restore']}, HP Cost: {s['hp_cost']})" if "energy_restore" in s and "heal" not in s and "energy_cost" not in s else
                             f" (Energy Gain: {s['energy_restore']}, Energy Cost: {s['energy_cost']})" if "energy_restore" in s and "heal" not in s and "hp_cost" not in s else
                             f" (Heal: {s['heal']}, Energy Cost: {s['energy_cost']})" if "heal" in s and "heal_per_turn" not in s and "energy_restore" not in s else
                             f" (Heal: 1d12, Energy Cost: {s['energy_cost']})" if "heal_dice" in s else
                             f" (Damage: 1d20, Energy Cost: {s['energy_cost']})" if "damage_dice" in s and s["name"] != "Secret Soul" else
                             f" (Damage: 1d10, Energy Cost: {s['energy_cost']})" if "damage_dice" in s and s["name"] == "Secret Soul" else
                             f" (Grants immunity to damage next opponent turn, Energy Cost: {s['energy_cost']})" if s["name"] == "Immunity" else
                             f" (Damage: {s['damage']}, +{s['extra_damage']} for {s['extra_turns']} turns, Energy Cost: {s['energy_cost']})" if "extra_damage" in s else "")
                            for i, s in enumerate(available_spells)
                        ]
                    )
                    spell_embed.add_field(name="Spells", value=spell_text, inline=False)
                    spell_msg = await fight_channel.send(embed=spell_embed)
                    for i in range(len(available_spells)):
                        await spell_msg.add_reaction(f"{i+1}️⃣")
                    await spell_msg.add_reaction("❌")

                    def spell_check(reaction, user):
                        return user == challenger and reaction.message.id == spell_msg.id and str(reaction.emoji) in [f"{i+1}️⃣" for i in range(len(available_spells))] + ["❌"]

                    try:
                        reaction, user = await bot.wait_for("reaction_add", timeout=300.0, check=spell_check)
                        if str(reaction.emoji) != "❌":
                            spell_idx = int(str(reaction.emoji)[0]) - 1
                            spell = available_spells[spell_idx]
                            if (spell.get("energy_cost", 0) == 0 or player["energy"] >= spell["energy_cost"]) and (spell.get("hp_cost", 0) == 0 or player["hp"] >= spell["hp_cost"]):
                                if "energy_cost" in spell:
                                    player["energy"] = max(0, player["energy"] - spell["energy_cost"])
                                if "hp_cost" in spell:
                                    player["hp"] = max(0, player["hp"] - spell["hp_cost"])
                                player["used_spells"][spell["name"]] = player["used_spells"].get(spell["name"], 0) + 1
                                if "damage" in spell:
                                    if not enemy["immune"]:
                                        enemy["hp"] -= spell["damage"]
                                    else:
                                        await fight_channel.send(f"Monster #{monster['number']}'s Immunity blocks the damage!")
                                    if spell["name"] == "Absorb" and "heal" in spell:
                                        player["hp"] = min(player["max_hp"], player["hp"] + spell["heal"])
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster and heals you!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Angel of Fire":
                                        player["angel_of_fire_turns"] = 2
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Ice Bolt":
                                        enemy["spells_disabled"] = spell["disable_spell_turns"]
                                        await fight_channel.send(embed=Embed(description=f"{challenger.display_name} uses Ice Bolt, disabling the monster's spells for the next turn!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Shadow Ball":
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Nova":
                                        enemy["spells_disabled"] = spell["disable_spell_turns"]
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster and disables its spells for the next turn!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Fire Ball":
                                        player["fire_ball_turns"] = 2
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Immolate":
                                        player["immolate_turns"] = 2
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Lightning Bolt":
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Acid Bomb":
                                        enemy["status_effects"]["poison"] = {"damage": spell["extra_damage"], "turns": spell["extra_turns"]}
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster and poisons it!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                    elif spell["name"] == "Meteor":
                                        enemy["energy"] = max(0, enemy["energy"] - spell["energy_drain"])
                                        await fight_channel.send(embed=Embed(description=f"{spell['name']} strikes the monster and burns its energy!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "heal_per_turn" in spell and "energy_per_turn" in spell:
                                    player["divinity_active"] = True
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} blesses you with divine energy!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "energy_drain" in spell and "energy_restore" in spell:
                                    enemy["energy"] = max(0, enemy["energy"] - spell["energy_drain"])
                                    player["energy"] += spell["energy_restore"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} drains the monster's energy!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "heal" in spell and "energy_restore" in spell:
                                    player["hp"] = min(player["max_hp"], player["hp"] + spell["heal"])
                                    player["energy"] += spell["energy_restore"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your vitality!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "heal_per_turn" in spell and "heal" in spell:
                                    player["hp"] = min(player["max_hp"], player["hp"] + spell["heal"])
                                    player["healing_spring_turns"] = spell["heal_turns"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} heals you over time!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "attack_reduction" in spell:
                                    enemy["attack_bonus"] -= spell["attack_reduction"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} reduces the monster's attack by {spell['attack_reduction']}!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "defence_reduction" in spell:
                                    enemy["defence"] = max(0, enemy["defence"] - spell["defence_reduction"])
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} reduces the monster's defence by {spell['defence_reduction']}!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "energy_restore" in spell and "heal" not in spell and "energy_cost" not in spell:
                                    player["energy"] += spell["energy_restore"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your energy at the cost of 5 HP!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "energy_restore" in spell and "heal" not in spell and "hp_cost" not in spell:
                                    player["energy"] += spell["energy_restore"]
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} restores your energy!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "heal" in spell and "heal_per_turn" not in spell and "energy_restore" not in spell:
                                    player["hp"] = min(player["max_hp"], player["hp"] + spell["heal"])
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} heals you!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "heal_dice" in spell:
                                    heal_amount = roll_dice(spell["heal_dice"])
                                    player["hp"] = min(player["max_hp"], player["hp"] + heal_amount)
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} rolls a {heal_amount} and heals you!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "damage_dice" in spell:
                                    damage = roll_dice(spell["damage_dice"])
                                    if not enemy["immune"]:
                                        enemy["hp"] -= damage
                                    else:
                                        await fight_channel.send(f"Monster #{monster['number']}'s Immunity blocks the damage!")
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} rolls a {damage} and strikes the monster!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "energy_per_turn" in spell and "hp_per_turn" in spell:
                                    player["infinity_aura_active"] = True
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} activates an aura that restores 2 HP and 2 energy each damage roll!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif "energy_per_turn" in spell and "hp_per_turn" not in spell:
                                    player["energy_aura_active"] = True
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} activates an energy aura at the cost of 6 HP!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Shield of Life":
                                    player["status_effects"]["shield_of_life"] = {"hp_per_turn": spell["hp_per_turn"], "active": True}
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} activates! You’ll gain {spell['hp_per_turn']} HP each turn when rolling damage!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Immunity":
                                    player["immune"] = True
                                    await fight_channel.send(embed=Embed(description=f"{spell['name']} protects you from damage next turn!", color=discord.Color.blue()).set_image(url=spell["image_url"]))
                    except asyncio.TimeoutError:
                        await fight_channel.send(f"{challenger.mention} didn’t choose a spell in time. Moving to actions...")

                if player["hp"] <= 0 or enemy["hp"] <= 0:
                    break

                if player["spells_disabled"] > 0:
                    await fight_channel.send("You can't use spells this turn!")

                action_options = [
                    {"name": "Attack: 5 dmg - Costs 3 Energy", "damage": 5, "energy_cost": 3, "index": 1},
                    {"name": "Attack: 8 dmg - Costs 6 Energy", "damage": 8, "energy_cost": 6, "index": 2},
                    {"name": "Attack: 13 dmg - Costs 10 Energy", "damage": 13, "energy_cost": 10, "index": 3},
                    {"name": "Heal: 7 hp - Costs 4 Energy", "heal": 7, "energy_cost": 4, "index": 4},
                    {"name": "Heal: 14 hp - Costs 10 Energy", "heal": 14, "energy_cost": 10, "index": 5},
                    {"name": "Recharge: Gains 6 Energy - Costs 0 Energy", "energy_gain": 6, "energy_cost": 0, "index": 6}
                ]
                action_embed = Embed(title=f"{challenger.display_name}, it's your turn!", color=discord.Color.blue())
                action_embed.set_thumbnail(url=player_monster["image_url"])
                action_extra_damage = 0
                if player["angel_of_fire_turns"] > 0:
                    action_extra_damage += 10
                if player["fire_ball_turns"] > 0:
                    action_extra_damage += 3
                if player["immolate_turns"] > 0:
                    action_extra_damage += 2
                if "poison" in enemy["status_effects"]:
                    action_extra_damage += enemy["status_effects"]["poison"]["damage"]
                hp_bonus_total = p_hp_per_turn + (3 if player["divinity_active"] else 0) + hp_bonus_this_turn
                if player["healing_spring_turns"] > 0:
                    hp_bonus_total += 7
                if not show_spell_box:
                    action_embed.add_field(
                        name="Stats",
                        value=(
                            f"**Damage:** {attack_roll} + {player['attack_bonus']}{' +'+str(action_extra_damage) if action_extra_damage > 0 else ''}\n"
                            f"**HP:** {player['hp']}/{player['max_hp']} + {hp_bonus_total}\n"
                            f"**Energy:** {player['energy']} + {p_energy_per_turn + (3 if player['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                            f"**Defense:** {player['defence']}"
                        ),
                        inline=False
                    )
                else:
                    action_embed.add_field(
                        name="Stats",
                        value=(
                            f"**HP:** {player['hp']}/{player['max_hp']} + {hp_bonus_total}\n"
                            f"**Energy:** {player['energy']} + {p_energy_per_turn + (3 if player['divinity_active'] else 0) + energy_bonus_this_turn}\n"
                            f"**Defense:** {player['defence']}"
                        ),
                        inline=False
                    )
                action_embed.add_field(name="\u200b", value="\u200b", inline=False)
                enemy_energy_modifier = energy_per_turn + (3 if enemy["divinity_active"] else 0) + e_energy_bonus_this_turn
                if any(item["name"] == "Crystal Sword" for item in player["deck"]):
                    enemy_energy_modifier_str = f" +{enemy_energy_modifier} -2" if enemy_energy_modifier > 0 else " -2"
                else:
                    enemy_energy_modifier_str = f" +{enemy_energy_modifier}" if enemy_energy_modifier > 0 else ""
                action_embed.add_field(
                    name=f"Monster #{monster['number']}",
                    value=f"**HP:** {enemy['hp']}/{enemy['max_hp']} + {hp_per_turn + (3 if enemy['divinity_active'] else 0)}\n"
                          f"**Energy:** {enemy['energy']}{enemy_energy_modifier_str}\n"
                          f"**Defense:** {enemy['defence']}",
                    inline=False
                )
                action_embed.add_field(name="\u200b", value="\u200b", inline=False)
                action_text = "Pick a move by reacting with:\n" + "\n".join(
                    f"{i+1}️⃣ - {a['name']}" for i, a in enumerate(action_options) if player["energy"] >= a["energy_cost"]
                )
                action_embed.add_field(name="Actions", value=action_text, inline=False)
                action_msg = await fight_channel.send(embed=action_embed)
                for i in range(len(action_options)):
                    if player["energy"] >= action_options[i]["energy_cost"]:
                        await action_msg.add_reaction(f"{i+1}️⃣")

                def action_check(reaction, user):
                    return user == challenger and reaction.message.id == action_msg.id and str(reaction.emoji) in [f"{i+1}️⃣" for i in range(len(action_options)) if player["energy"] >= action_options[i]["energy_cost"]]

                try:
                    reaction, user = await bot.wait_for("reaction_add", timeout=300.0, check=action_check)
                    action_idx = int(str(reaction.emoji)[0]) - 1
                    action = action_options[action_idx]
                    if player["energy"] >= action["energy_cost"]:
                        player["energy"] = max(0, player["energy"] - action["energy_cost"])
                        if "damage" in action:
                            if not enemy["immune"]:
                                enemy["hp"] -= action["damage"]
                                await fight_channel.send(f"{challenger.display_name} uses **{action['name']}**!")
                            else:
                                await fight_channel.send(f"Monster #{monster['number']}'s Immunity blocks the damage!")
                        elif "heal" in action:
                            player["hp"] = min(player["max_hp"], player["hp"] + action["heal"])
                            await fight_channel.send(f"{challenger.display_name} uses **{action['name']}**!")
                        elif "energy_gain" in action:
                            player["energy"] += action["energy_gain"]
                            await fight_channel.send(f"{challenger.display_name} uses **{action['name']}**!")
                except asyncio.TimeoutError:
                    await fight_channel.send(f"{challenger.mention} didn’t choose an action in time. Monster’s turn!")

                if player["spells_disabled"] > 0:
                    player["spells_disabled"] -= 1
                    if player["spells_disabled"] == 0:
                        await fight_channel.send(f"{challenger.display_name}'s spells are no longer disabled!")

                if player["hp"] <= 0 or enemy["hp"] <= 0:
                    break

                if enemy["immune"]:
                    enemy["immune"] = False
                    await fight_channel.send(f"Monster #{monster['number']}'s Immunity has worn off!")
            else:
                hp_bonus_this_turn = 0
                e_energy_bonus_this_turn = 0
                extra_damage_this_turn = 0

                attack_roll = roll_attack_die(enemy["attack"])
                if any(item["name"] == "Crystal Sword" for item in enemy["deck"]):
                    player["energy"] = max(0, player["energy"] - 2)
                    await fight_channel.send(f"Monster #{monster['number']}'s Crystal Sword burns 2 energy from {challenger.display_name}!")

                if enemy["infinity_aura_active"]:
                    enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + 2)
                    enemy["energy"] += 2
                    hp_bonus_this_turn += 2
                    e_energy_bonus_this_turn += 2
                blood_ring_count = sum(1 for item in enemy["deck"] if item["name"] == "Blood Ring")
                enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + blood_ring_count * 2)
                hp_bonus_this_turn += blood_ring_count * 2
                frost_ring_count = sum(1 for item in enemy["deck"] if item["name"] == "Frost Ring")
                enemy["energy"] += frost_ring_count * 1
                e_energy_bonus_this_turn += frost_ring_count * 1
                if any(item["name"] == "Energy Ring" for item in enemy["deck"]):
                    enemy["energy"] += 1
                    e_energy_bonus_this_turn += 1
                if enemy["energy_aura_active"]:
                    enemy["energy"] += 2
                    e_energy_bonus_this_turn += 2
                total_attack = attack_roll + enemy["attack_bonus"]
                if enemy["angel_of_fire_turns"] > 0:
                    total_attack += 10
                    extra_damage_this_turn += 10
                    enemy["angel_of_fire_turns"] -= 1
                if enemy["fire_ball_turns"] > 0:
                    total_attack += 3
                    extra_damage_this_turn += 3
                    enemy["fire_ball_turns"] -= 1
                if enemy["immolate_turns"] > 0:
                    total_attack += 2
                    extra_damage_this_turn += 2
                    enemy["immolate_turns"] -= 1
                if "poison" in player["status_effects"]:
                    poison_damage = player["status_effects"]["poison"]["damage"]
                    total_attack += poison_damage
                    player["status_effects"]["poison"]["turns"] -= 1
                    if player["status_effects"]["poison"]["turns"] <= 0:
                        del player["status_effects"]["poison"]
                if enemy["divinity_active"]:
                    enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + 3)
                    enemy["energy"] += 3
                    hp_bonus_this_turn += 3
                    e_energy_bonus_this_turn += 3
                if enemy["healing_spring_turns"] > 0:
                    enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + 7)
                    hp_bonus_this_turn += 7
                    enemy["healing_spring_turns"] -= 1

                if not player["immune"]:
                    player["hp"] -= max(0, total_attack - player["defence"])
                else:
                    await fight_channel.send(f"{challenger.display_name}'s Immunity blocks the damage!")

                enemy_embed = Embed(title=f"Monster #{monster['number']}'s Turn!", color=discord.Color.red())
                enemy_embed.set_thumbnail(url=monster["image_url"])
                enemy_energy_modifier = energy_per_turn + (3 if enemy["divinity_active"] else 0) + e_energy_bonus_this_turn
                if any(item["name"] == "Crystal Sword" for item in player["deck"]):
                    enemy_energy_modifier_str = f" +{enemy_energy_modifier} -2" if enemy_energy_modifier > 0 else " -2"
                else:
                    enemy_energy_modifier_str = f" +{enemy_energy_modifier}" if enemy_energy_modifier > 0 else ""
                enemy_embed.add_field(
                    name="Stats",
                    value=f"**Damage:** {attack_roll} + {enemy['attack_bonus']}{' +'+str(extra_damage_this_turn) if extra_damage_this_turn > 0 else ''}\n"
                          f"**HP:** {enemy['hp']}/{enemy['max_hp']} + {hp_per_turn + (3 if enemy['divinity_active'] else 0) + hp_bonus_this_turn}\n"
                          f"**Energy:** {enemy['energy']}{enemy_energy_modifier_str}\n"
                          f"**Defense:** {enemy['defence']}",
                    inline=False
                )

                action_options = [
                    {"name": "Attack: 5 dmg - Costs 3 Energy", "damage": 5, "energy_cost": 3, "index": 1},
                    {"name": "Attack: 8 dmg - Costs 6 Energy", "damage": 8, "energy_cost": 6, "index": 2},
                    {"name": "Attack: 13 dmg - Costs 10 Energy", "damage": 13, "energy_cost": 10, "index": 3},
                    {"name": "Heal: 7 hp - Costs 4 Energy", "heal": 7, "energy_cost": 4, "index": 4},
                    {"name": "Heal: 14 hp - Costs 10 Energy", "heal": 14, "energy_cost": 10, "index": 5},
                    {"name": "Recharge: Gains 6 Energy - Costs 0 Energy", "energy_gain": 6, "energy_cost": 0, "index": 6}
                ]
                available_actions = [a for a in action_options if enemy["energy"] >= a["energy_cost"]]

                action = None
                monster_hp_percent = enemy["hp"] / enemy["max_hp"] * 100
                player_hp_percent = player["hp"] / player["max_hp"] * 100

                if enemy["energy"] < 5 and any(a["index"] == 6 for a in available_actions):
                    action = next(a for a in action_options if a["index"] == 6)
                elif monster_hp_percent < 40 and any(a["index"] == 5 for a in available_actions):
                    action = next(a for a in action_options if a["index"] == 5)
                elif player_hp_percent <= 20 and any(a["index"] in [1, 2, 3] for a in available_actions):
                    damage_actions = [a for a in available_actions if a["index"] in [1, 2, 3]]
                    action = random.choice(damage_actions) if damage_actions else None
                elif monster_hp_percent > 60 and any(a["index"] in [1, 2, 3] for a in available_actions):
                    damage_actions = [a for a in available_actions if a["index"] in [1, 2, 3]]
                    action = random.choice(damage_actions) if damage_actions else None
                else:
                    action = random.choice(available_actions) if available_actions else None

                if action:
                    enemy["energy"] = max(0, enemy["energy"] - action["energy_cost"])
                    if "damage" in action:
                        if not player["immune"]:
                            player["hp"] -= action["damage"]
                            enemy_embed.add_field(name="Action", value=f"Monster #{monster['number']} uses **{action['name']}**", inline=False)
                        else:
                            enemy_embed.add_field(name="Action", value=f"Monster #{monster['number']} uses **{action['name']}**, but Immunity blocks the damage!", inline=False)
                    elif "heal" in action:
                        enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + action["heal"])
                        enemy_embed.add_field(name="Action", value=f"Monster #{monster['number']} uses **{action['name']}**", inline=False)
                    elif "energy_gain" in action:
                        enemy["energy"] += action["energy_gain"]
                        enemy_embed.add_field(name="Action", value=f"Monster #{monster['number']} uses **{action['name']}**", inline=False)

                await fight_channel.send(embed=enemy_embed)

                if enemy["spells_disabled"] > 0:
                    await fight_channel.send(f"Monster #{monster['number']} can't use spells this turn!")
                    enemy["spells_disabled"] -= 1
                    if enemy["spells_disabled"] == 0:
                        await fight_channel.send(f"Monster #{monster['number']}'s spells are no longer disabled!")
                else:
                    enemy_spells = [item for item in enemy["deck"] if item["type"] == "Spell"]
                    available_enemy_spells = []
                    healing_spells = ["Fountain", "Healing Spring", "He Potion", "Holy Dice", "Holy Heal", "Absorb", "HP Potion", "Potion of Life"]
                    can_use_healing_spells = enemy["hp"] <= enemy["max_hp"] * 0.5

                    try:
                        for spell in enemy_spells:
                            used_count = enemy["used_spells"].get(spell["name"], 0)
                            max_count = enemy["spell_counts"].get(spell["name"], 0)
                            if spell["name"] in healing_spells and not can_use_healing_spells:
                                continue

                            if spell["name"] == "Absorb" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Absorb", "damage": 10, "energy_cost": 10, "heal": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Angel of Fire" and used_count < max_count and enemy["energy"] >= 15:
                                available_enemy_spells.append({"name": "Angel of Fire", "damage": 12, "energy_cost": 15, "dot": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Divinity" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Divinity", "heal_per_turn": 3, "energy_per_turn": 3, "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Ice Bolt" and used_count < max_count and enemy["energy"] >= 7:
                                available_enemy_spells.append({"name": "Ice Bolt", "damage": 5, "energy_cost": 7, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                            elif spell["name"] == "Shadow Ball" and used_count < max_count and enemy["energy"] >= 8:
                                available_enemy_spells.append({"name": "Shadow Ball", "damage": 10, "energy_cost": 8, "image_url": spell["image_url"]})
                            elif spell["name"] == "Soul Drain" and used_count < max_count and enemy["hp"] >= 10:
                                available_enemy_spells.append({"name": "Soul Drain", "energy_drain": 15, "energy_restore": 15, "hp_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "He Potion" and used_count < max_count and enemy["energy"] >= 0:
                                available_enemy_spells.append({"name": "He Potion", "heal": 5, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                            elif spell["name"] == "Potion of Life" and used_count < max_count and enemy["energy"] >= 0:
                                available_enemy_spells.append({"name": "Potion of Life", "heal": 10, "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                            elif spell["name"] == "Healing Spring" and used_count < max_count and enemy["energy"] >= 8:
                                available_enemy_spells.append({"name": "Healing Spring", "heal": 7, "heal_per_turn": 7, "heal_turns": 2, "energy_cost": 8, "image_url": spell["image_url"]})
                            elif spell["name"] == "Desenchant" and used_count < max_count and enemy["energy"] >= 7:
                                available_enemy_spells.append({"name": "Desenchant", "attack_reduction": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                            elif spell["name"] == "Energy Drain" and used_count < max_count and enemy["hp"] >= 5:
                                available_enemy_spells.append({"name": "Energy Drain", "energy_drain": 5, "energy_restore": 5, "hp_cost": 5, "image_url": spell["image_url"]})
                            elif spell["name"] == "Energy Potion" and used_count < max_count and enemy["energy"] >= 0:
                                available_enemy_spells.append({"name": "Energy Potion", "energy_restore": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                            elif spell["name"] == "Fountain" and used_count < max_count and enemy["energy"] >= 5:
                                available_enemy_spells.append({"name": "Fountain", "heal": 10, "energy_cost": 5, "image_url": spell["image_url"]})
                            elif spell["name"] == "Holy Dice" and used_count < max_count and enemy["energy"] >= 6:
                                available_enemy_spells.append({"name": "Holy Dice", "heal_dice": "1d12", "energy_cost": 6, "image_url": spell["image_url"]})
                            elif spell["name"] == "Dagger Strike" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Dagger Strike", "damage_dice": "1d20", "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Holy Heal" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Holy Heal", "heal": 30, "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "HP Potion" and used_count < max_count and enemy["energy"] >= 0:
                                available_enemy_spells.append({"name": "HP Potion", "heal": 5, "energy_cost": 0, "image_url": spell["image_url"]})
                            elif spell["name"] == "Energy Aura" and used_count < max_count and enemy["hp"] >= 6:
                                available_enemy_spells.append({"name": "Energy Aura", "energy_per_turn": 2, "hp_cost": 6, "image_url": spell["image_url"]})
                            elif spell["name"] == "Immolate" and used_count < max_count and enemy["energy"] >= 7:
                                available_enemy_spells.append({"name": "Immolate", "damage": 3, "extra_damage": 2, "extra_turns": 2, "energy_cost": 7, "image_url": spell["image_url"]})
                            elif spell["name"] == "Lightning Bolt" and used_count < max_count and enemy["energy"] >= 5:
                                available_enemy_spells.append({"name": "Lightning Bolt", "damage": 6, "energy_cost": 5, "image_url": spell["image_url"]})
                            elif spell["name"] == "Fire Ball" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Fire Ball", "damage": 10, "extra_damage": 3, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Shield of Life" and used_count < max_count and enemy["energy"] >= 5:
                                available_enemy_spells.append({"name": "Shield of Life", "hp_per_turn": 2, "energy_cost": 5, "image_url": spell["image_url"]})
                            elif spell["name"] == "Nova" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Nova", "damage": 10, "energy_cost": 10, "disable_spell_turns": 1, "image_url": spell["image_url"]})
                            elif spell["name"] == "Acid Bomb" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Acid Bomb", "damage": 5, "extra_damage": 5, "extra_turns": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Restoration" and used_count < max_count and enemy["hp"] >= 5:
                                available_enemy_spells.append({"name": "Restoration", "energy_restore": 10, "hp_cost": 5, "image_url": spell["image_url"]})
                            elif spell["name"] == "Secret Soul" and used_count < max_count and enemy["energy"] >= 6:
                                available_enemy_spells.append({"name": "Secret Soul", "damage_dice": "1d10", "energy_cost": 6, "image_url": spell["image_url"]})
                            elif spell["name"] == "Shield Breaker" and used_count < max_count and enemy["energy"] >= 4:
                                available_enemy_spells.append({"name": "Shield Breaker", "defence_reduction": 1, "energy_cost": 4, "image_url": spell["image_url"]})
                            elif spell["name"] == "Infinity Aura" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Infinity Aura", "hp_per_turn": 2, "energy_per_turn": 2, "energy_cost": 10, "image_url": spell["image_url"]})
                            elif spell["name"] == "Meteor" and used_count < max_count and enemy["energy"] >= 20:
                                available_enemy_spells.append({"name": "Meteor", "damage": 20, "energy_drain": 10, "energy_cost": 20, "image_url": spell["image_url"]})
                            elif spell["name"] == "Immunity" and used_count < max_count and enemy["energy"] >= 10:
                                available_enemy_spells.append({"name": "Immunity", "energy_cost": 10, "image_url": spell["image_url"]})

                    except Exception as e:
                        logger.error(f"Error while building available_enemy_spells: {e}")
                        available_enemy_spells = []

                    if available_enemy_spells:
                        try:
                            spell = random.choice(available_enemy_spells)
                            if "energy_cost" in spell:
                                enemy["energy"] = max(0, enemy["energy"] - spell.get("energy_cost", 0))
                            if "hp_cost" in spell:
                                enemy["hp"] = max(0, enemy["hp"] - spell.get("hp_cost", 0))
                            if "disable_spell_turns" in spell and not player["prevent_freeze"]:
                                player["status_effects"]["disable_spell"] = {"turns": spell["disable_spell_turns"]}
                                await fight_channel.send(f"{challenger.display_name}'s spells are disabled for {spell['disable_spell_turns']} turns!")
                            elif "disable_spell_turns" in spell and player["prevent_freeze"]:
                                await fight_channel.send(f"{challenger.display_name} is immune to freezing effects!")
                        except Exception as e:
                            logger.error(f"Error while applying enemy spell: {e}")
                            enemy["used_spells"][spell["name"]] = enemy["used_spells"].get(spell["name"], 0) + 1

                            if "damage" in spell:
                                if not player["immune"]:
                                    player["hp"] -= spell["damage"]
                                else:
                                    await fight_channel.send(f"{challenger.display_name}'s Immunity blocks the damage!")
                                if spell["name"] == "Absorb" and "heal" in spell:
                                    enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + spell["heal"])
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and heals itself!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Angel of Fire":
                                    enemy["angel_of_fire_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Ice Bolt":
                                    player["spells_disabled"] = spell["disable_spell_turns"]
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts Ice Bolt, disabling your spells for the next turn!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Shadow Ball":
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Nova":
                                    player["spells_disabled"] = spell["disable_spell_turns"]
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and disables your spells for the next turn!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Fire Ball":
                                    enemy["fire_ball_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Immolate":
                                    enemy["immolate_turns"] = 2
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Lightning Bolt":
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Acid Bomb":
                                    player["status_effects"]["poison"] = {"damage": spell["extra_damage"], "turns": spell["extra_turns"]}
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and poisons you!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                                elif spell["name"] == "Meteor":
                                    player["energy"] = max(0, player["energy"] - spell["energy_drain"])
                                    await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and burns your energy!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "heal_per_turn" in spell and "energy_per_turn" in spell:
                                enemy["divinity_active"] = True
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and is blessed with divine energy!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "energy_drain" in spell and "energy_restore" in spell:
                                player["energy"] = max(0, player["energy"] - spell["energy_drain"])
                                enemy["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and drains your energy!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "heal" in spell and "energy_restore" in spell:
                                enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + spell["heal"])
                                enemy["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and restores its vitality!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "heal_per_turn" in spell and "heal" in spell:
                                enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + spell["heal"])
                                enemy["healing_spring_turns"] = spell["heal_turns"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and heals over time!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "attack_reduction" in spell:
                                player["attack_bonus"] -= spell["attack_reduction"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and reduces your attack by {spell['attack_reduction']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "defence_reduction" in spell:
                                player["defence"] = max(0, player["defence"] - spell["defence_reduction"])
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and reduces your defence by {spell['defence_reduction']}!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "energy_restore" in spell and "heal" not in spell and "energy_cost" not in spell:
                                enemy["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and restores its energy at the cost of 5 HP!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "energy_restore" in spell and "heal" not in spell and "hp_cost" not in spell:
                                enemy["energy"] += spell["energy_restore"]
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and restores its energy!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "heal" in spell and "heal_per_turn" not in spell and "energy_restore" not in spell:
                                enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + spell["heal"])
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and heals itself!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "heal_dice" in spell:
                                heal_amount = roll_dice(spell["heal_dice"])
                                enemy["hp"] = min(enemy["max_hp"], enemy["hp"] + heal_amount)
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and rolls a {heal_amount} to heal!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "damage_dice" in spell:
                                damage = roll_dice(spell["damage_dice"])
                                if not player["immune"]:
                                    player["hp"] -= damage
                                else:
                                    await fight_channel.send(f"{challenger.display_name}'s Immunity blocks the damage!")
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and rolls a {damage} to strike you!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "energy_per_turn" in spell and "hp_per_turn" in spell:
                                enemy["infinity_aura_active"] = True
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and activates an aura that restores 2 HP and 2 energy each damage roll!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif "energy_per_turn" in spell and "hp_per_turn" not in spell:
                                enemy["energy_aura_active"] = True
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and activates an energy aura at the cost of 6 HP!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif spell["name"] == "Shield of Life":
                                enemy["status_effects"]["shield_of_life"] = {"hp_per_turn": spell["hp_per_turn"], "active": True}
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and will gain {spell['hp_per_turn']} HP each turn when rolling damage!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                            elif spell["name"] == "Immunity":
                                enemy["immune"] = True
                                await fight_channel.send(embed=Embed(description=f"Monster #{monster['number']} casts {spell['name']} and is immune to damage next turn!", color=discord.Color.red()).set_image(url=spell["image_url"]))
                        except Exception as e:
                            logger.error(f"Error while processing enemy spell {spell['name']}: {e}")

                if player["hp"] <= 0 or enemy["hp"] <= 0:
                    break

                if player["immune"]:
                    player["immune"] = False
                    await fight_channel.send(f"{challenger.display_name}'s Immunity has worn off!")

            player_turn = not player_turn

        # Résultat du combat
        if player["hp"] <= 0:
            await fight_channel.send(f"{challenger.mention} has been defeated by Monster #{monster['number']}!")
            player_stats[player_id]["losses"] = player_stats[player_id].get("losses", 0) + 1
        else:
            await fight_channel.send(f"{challenger.mention} has defeated Monster #{monster['number']}!")
            player_stats[player_id]["wins"] = player_stats[player_id].get("wins", 0) + 1
            player_stats[player_id]["points"] = player_stats[player_id].get("points", 0) + points_earned
            await fight_channel.send(f"{challenger.mention} earned {points_earned} Exp!")

            # Logique de gain de gem
            today = datetime.now(timezone.utc).date()
            last_gem_gain = player_stats[player_id].get("last_gem_gain")
            can_gain = last_gem_gain is None or datetime.fromtimestamp(last_gem_gain, tz=timezone.utc).date() < today

            if can_gain and random.choices([True, False], weights=[1, 99], k=1)[0]:
                player_stats[player_id]["gems"] = player_stats[player_id].get("gems", 0) + 1
                player_stats[player_id]["last_gem_gain"] = time.time()
                await fight_channel.send("You found 1 Gem 💎!")
                logger.info(f"Player {challenger.display_name} (ID: {player_id}) gained 1 gem after defeating wild monster #{monster['number']}.")

        save_player_data()
        await update_leaderboard()

        # Supprimer le canal de combat après 60 secondes
        await asyncio.sleep(60)
        try:
            await fight_channel.delete()
            logger.info(f"Deleted battle channel {fight_channel.name}.")
        except discord.errors.Forbidden:
            logger.error(f"Error: Bot lacks permissions to delete channel {fight_channel.name}.")
        except Exception as e:
            logger.error(f"Unexpected error while deleting battle channel: {e}")

        # Supprimer le message de démarrage après 60 secondes
        try:
            await start_message.delete()
        except discord.errors.Forbidden:
            logger.error(f"Error: Bot lacks permissions to delete start message in channel {SPAWN_CHANNEL_ID}.")
        except Exception as e:
            logger.error(f"Unexpected error while deleting start message: {e}")

    except Exception as e:
        logger.error(f"Unexpected error in spawn_random_monster: {e}")
        if fight_channel:
            try:
                await fight_channel.delete()
                logger.info(f"Deleted battle channel {fight_channel.name} due to error.")
            except Exception as de:
                logger.error(f"Error while deleting battle channel after exception: {de}")
        if start_message:
            try:
                await start_message.delete()
            except Exception as de:
                logger.error(f"Error while deleting start message after exception: {de}")
        await channel.send("An error occurred during the battle. The battle has been canceled.")
