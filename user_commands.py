# user_commands.py
import discord
from discord import app_commands, Embed
from discord.interactions import Interaction
from config import bot
from data import player_monsters, player_decks, player_stats, equipment_points, save_player_data
from utils import calculate_bonuses, calculate_total_weight
import asyncio
import math
import logging

# Configuration des logs
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Compteur global pour les canaux temporaires
channel_counter = 0

async def delete_temp_channel(channel, channel_name, delay=300):
    """Supprime un canal temporaire après un délai spécifié (en secondes)."""
    logger.info(f"Programmation de la suppression du canal {channel_name} dans {delay} secondes.")
    await asyncio.sleep(delay)
    logger.info(f"Tentative de suppression du canal {channel_name}.")
    try:
        await channel.delete()
        logger.info(f"Canal {channel_name} supprimé avec succès.")
    except discord.errors.NotFound:
        logger.warning(f"Canal {channel_name} déjà supprimé.")
    except discord.errors.Forbidden:
        logger.error(f"Le bot n'a pas les permissions pour supprimer le canal {channel_name}.")
    except Exception as e:
        logger.error(f"Erreur inattendue lors de la suppression du canal {channel_name}: {e}")

def calculate_season_level_and_exp(total_exp):
    """
    Calcule le niveau de la saison, l'expérience actuelle (y) et l'expérience nécessaire pour le prochain niveau (z).
    Seuils : 200 exp pour niveau 2, 300 pour niveau 3, 450 pour niveau 4, etc. (augmentation de 50% par niveau).
    """
    if total_exp < 0:
        total_exp = 0

    season_level = 1
    exp_needed_so_far = 0
    next_level_exp = 200  # Seuil pour atteindre le niveau 2

    while exp_needed_so_far + next_level_exp <= total_exp and next_level_exp > 0:
        exp_needed_so_far += next_level_exp
        season_level += 1
        next_level_exp = math.ceil(next_level_exp * 1.5)  # +50% pour le prochain niveau

    current_exp = total_exp - exp_needed_so_far  # Expérience actuelle depuis le dernier niveau
    return season_level, current_exp, next_level_exp

async def create_profile_embed(interaction, player_id, monster, deck, player_stat, season_level, current_exp, next_level_exp, deck_level, total_level):
    """Crée l'embed pour /show_profile avec les stats arrondies, la mise en forme demandée et les 💎."""
    # Calculer les bonus d'équipement
    attack_bonus, hp_bonus, energy_bonus, defence_bonus, hp_per_turn, energy_per_turn, prevent_freeze, last_turn_when_dead = calculate_bonuses(deck)

    # Appliquer les bonus d'attributs avec arrondi vers le haut
    attack_bonus_with_attributes = math.ceil(attack_bonus + player_stat["attributes"]["damage"] * 0.75)
    hp_with_attributes = math.ceil(monster["hp"] + hp_bonus + player_stat["attributes"]["hp"] * 2.5)
    energy_with_attributes = math.ceil(monster["energy"] + energy_bonus + player_stat["attributes"]["energy"] * 2.0)
    defence_with_attributes = math.ceil(monster["defence"] + defence_bonus + player_stat["attributes"]["defence"] * 0.5)

    # Ajoutons des informations sur les nouveaux effets spéciaux dans l'affichage du profil
    special_effects = []
    if prevent_freeze:
        special_effects.append("❄️ Immune to Freeze")
    if last_turn_when_dead:
        special_effects.append("💀 Last Turn When Defeated")

    # Créer l'embed
    embed = Embed(title=f"{interaction.user.display_name}'s Monster and Deck", color=discord.Color.green())
    embed.set_thumbnail(url=monster["image_url"])

    # Ajouter le niveau et l'expérience
    level_exp_str = f"Lv: {total_level}    Exp: ({current_exp} / {next_level_exp})"
    embed.add_field(name="Level & Exp", value=level_exp_str, inline=False)

    # Ajouter les stats avec arrondi
    stats = (
        f"**Damage:** {monster['attack']} + {attack_bonus_with_attributes}\n"
        f"**HP:** {hp_with_attributes} + {hp_per_turn}\n"
        f"**Energy:** {energy_with_attributes} + {energy_per_turn}\n"
        f"**Defense:** {defence_with_attributes}"
    )
    embed.add_field(name="Stats", value=stats, inline=False)

    # Ajouter un espace avant Gems
    embed.add_field(name="\u200b", value="\u200b", inline=False)

    # Ajouter les Gems (💎)
    embed.add_field(name="Gems", value=f"{player_stat.get('gems', 0)} 💎", inline=False)

    # Ajouter un espace entre Gems et Equipment
    embed.add_field(name="\u200b", value="\u200b", inline=False)

    # Ajouter l'équipement
    equipment_str = "\n".join(f"- {item['name']} ({item['type']})" for item in deck if item["type"] != "Spell") or "None"
    embed.add_field(name="Equipment", value=equipment_str, inline=False)

    # Ajouter les sorts
    spell_str = "\n".join(f"- {item['name']}" for item in deck if item["type"] == "Spell") or "None"
    embed.add_field(name="Spells", value=spell_str, inline=False)

    # Ajouter le poids total
    total_weight = sum(item["weight"] for item in deck)
    embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

    # Ajouter un espace
    embed.add_field(name="\u200b", value="\u200b", inline=False)

    # Ajouter la section Attribute Points si des points sont disponibles ou alloués
    used_points = sum([
        player_stat["attributes"]["damage"],
        player_stat["attributes"]["hp"],
        player_stat["attributes"]["energy"],
        player_stat["attributes"]["defence"]
    ])
    if player_stat["attributes"]["available"] > 0 or used_points > 0:
        if player_stat["attributes"]["available"] > 0:
            embed.add_field(
                name="\u200b",  # Espace invisible pour supprimer le titre redondant
                value=(
                    f"**Attribute Points**: {player_stat['attributes']['available']}\n"
                    "1️⃣ Damage +0.75\n"
                    "2️⃣ HP +2.5\n"
                    "3️⃣ Energy +2\n"
                    "4️⃣ Defence +0.5\n\n"
                    "React with 1️⃣ to 4️⃣ to allocate points or 🔄 to reset attributes"
                ),
                inline=False
            )
        else:
            embed.add_field(
                name="\u200b",
                value=(
                    f"**Attribute Points**: {player_stat['attributes']['available']}\n"
                    "React with 🔄 to reset attributes"
                ),
                inline=False
            )

    # Ajouter ces effets à l'embed
    if special_effects:
        embed.add_field(name="Special Effects", value="\n".join(special_effects), inline=False)

    return embed

@app_commands.command(name="show_profile", description="Display your equipped monster, deck, level, and experience")
async def show_profile(interaction: discord.Interaction):
    global channel_counter
    player_id = interaction.user.id
    if player_id not in player_monsters:
        await interaction.response.send_message("You need to equip a monster first! Contact an admin with /monster.", ephemeral=True)
        return

    # Récupérer les données du joueur
    monster = player_monsters[player_id]
    deck = player_decks.get(player_id, [])
    player_stat = player_stats.get(player_id, {"points": 0, "pvp_wins": 0, "pvp_losses": 0, "pvp_points": 0})

    # Initialiser attributes et gems si absents
    if "attributes" not in player_stat:
        player_stat["attributes"] = {
            "available": 0,
            "damage": 0,
            "hp": 0,
            "energy": 0,
            "defence": 0
        }
    if "gems" not in player_stat:
        player_stat["gems"] = 0
    player_stats[player_id] = player_stat
    save_player_data()

    # Calculer le niveau de la saison et l'expérience
    total_exp = player_stat.get("points", 0)
    season_level, current_exp, next_level_exp = calculate_season_level_and_exp(total_exp)

    # Calculer les points d'attributs disponibles
    total_attribute_points = season_level * 3
    used_points = sum([
        player_stat["attributes"]["damage"],
        player_stat["attributes"]["hp"],
        player_stat["attributes"]["energy"],
        player_stat["attributes"]["defence"]
    ])
    player_stat["attributes"]["available"] = total_attribute_points - used_points
    player_stats[player_id] = player_stat
    save_player_data()

    # Calculer le niveau du deck
    deck_points = sum(equipment_points.get(item["name"], 0) for item in deck)
    deck_level = round(deck_points / 3)

    # Niveau total
    total_level = deck_level + season_level

    # Créer un canal temporaire
    channel_counter += 1
    channel_name = f"player-profile-{channel_counter}"
    logger.info(f"Création du canal temporaire {channel_name} pour {interaction.user.display_name}.")
    try:
        temp_channel = await interaction.guild.create_text_channel(
            name=channel_name,
            reason=f"Temporary profile channel for {interaction.user.display_name}"
        )
        logger.info(f"Canal {channel_name} créé avec succès.")
    except discord.errors.Forbidden:
        logger.error(f"Le bot n'a pas les permissions pour créer le canal {channel_name}.")
        await interaction.response.send_message("Erreur : le bot n'a pas les permissions pour créer un canal temporaire.", ephemeral=True)
        return
    except Exception as e:
        logger.error(f"Erreur inattendue lors de la création du canal {channel_name}: {e}")
        await interaction.response.send_message("Erreur inattendue lors de la création du canal temporaire.", ephemeral=True)
        return

    # Planifier la suppression du canal
    asyncio.create_task(delete_temp_channel(temp_channel, channel_name))

    # Envoyer un message de redirection qui se supprime après 2 minutes, visible uniquement par le joueur
    await interaction.response.send_message(
        f"Check your profile in {temp_channel.mention} !",
        ephemeral=True,
        delete_after=120
    )

    # Créer et envoyer l'embed initial dans le canal temporaire
    embed = await create_profile_embed(interaction, player_id, monster, deck, player_stat, season_level, current_exp, next_level_exp, deck_level, total_level)
    message = await temp_channel.send(embed=embed)

    # Ajouter les réactions si des points sont disponibles ou alloués
    if player_stat["attributes"]["available"] > 0 or used_points > 0:
        reactions = []
        if player_stat["attributes"]["available"] > 0:
            reactions.extend(['1️⃣', '2️⃣', '3️⃣', '4️⃣'])
        if used_points > 0 or player_stat["attributes"]["available"] > 0:
            reactions.append('🔄')
        for reaction in reactions:
            await message.add_reaction(reaction)

        def check_reaction(reaction, user):
            return user == interaction.user and str(reaction.emoji) in reactions and reaction.message.id == message.id

        def check_confirm_reaction(reaction, user):
            return user == interaction.user and str(reaction.emoji) == '✅' and reaction.message.id == confirm_message.id

        while player_stat["attributes"]["available"] > 0 or used_points > 0:
            try:
                reaction, user = await bot.wait_for('reaction_add', timeout=180.0, check=check_reaction)
                try:
                    # Supprimer l'ancien message
                    await message.delete()
                except discord.errors.NotFound:
                    logger.warning(f"Message already deleted for player {player_id}.")
                except discord.errors.Forbidden:
                    logger.error(f"Bot lacks permissions to delete message for player {player_id}.")
                except Exception as e:
                    logger.error(f"Unexpected error while deleting message: {e}")

                # Mettre à jour used_points pour la condition de la boucle
                used_points = sum([
                    player_stat["attributes"]["damage"],
                    player_stat["attributes"]["hp"],
                    player_stat["attributes"]["energy"],
                    player_stat["attributes"]["defence"]
                ])

                # Gestion de la réinitialisation
                if str(reaction.emoji) == '🔄':
                    # Envoyer un message dans le canal temporaire pour confirmation
                    confirm_message = await temp_channel.send(
                        "Are you sure you want to reset your attributes? React with ✅ to confirm.",
                        delete_after=30
                    )
                    await confirm_message.add_reaction('✅')

                    try:
                        confirm_reaction, _ = await bot.wait_for('reaction_add', timeout=30.0, check=check_confirm_reaction)
                        # Réinitialiser les attributs
                        player_stat["attributes"]["damage"] = 0
                        player_stat["attributes"]["hp"] = 0
                        player_stat["attributes"]["energy"] = 0
                        player_stat["attributes"]["defence"] = 0
                        player_stat["attributes"]["available"] = season_level * 3
                        player_stats[player_id] = player_stat
                        save_player_data()
                        # Envoyer un message de confirmation dans le canal temporaire
                        await temp_channel.send("Attributes reset successfully!")
                        # Mettre à jour used_points après réinitialisation
                        used_points = 0
                    except asyncio.TimeoutError:
                        # Ne rien faire en cas de timeout (message déjà supprimé par delete_after)
                        pass
                    except discord.errors.Forbidden:
                        logger.error(f"Bot lacks permissions to send confirmation message for player {player_id}.")
                    except Exception as e:
                        logger.error(f"Unexpected error during reset confirmation: {e}")

                # Gestion de l'ajout d'un seul point
                else:
                    attribute_map = {
                        '1️⃣': "damage",
                        '2️⃣': "hp",
                        '3️⃣': "energy",
                        '4️⃣': "defence"
                    }
                    selected_attribute = attribute_map.get(str(reaction.emoji))
                    if selected_attribute and player_stat["attributes"]["available"] > 0:
                        # Mettre à jour les attributs
                        player_stat["attributes"]["available"] -= 1
                        player_stat["attributes"][selected_attribute] += 1
                        player_stats[player_id] = player_stat
                        save_player_data()

                # Recréer et envoyer un nouvel embed
                embed = await create_profile_embed(interaction, player_id, monster, deck, player_stat, season_level, current_exp, next_level_exp, deck_level, total_level)
                message = await temp_channel.send(embed=embed)

                # Ajouter les réactions si des points restent ou sont alloués
                if player_stat["attributes"]["available"] > 0 or used_points > 0:
                    reactions = []
                    if player_stat["attributes"]["available"] > 0:
                        reactions.extend(['1️⃣', '2️⃣', '3️⃣', '4️⃣'])
                    if used_points > 0 or player_stat["attributes"]["available"] > 0:
                        reactions.append('🔄')
                    for reaction in reactions:
                        await message.add_reaction(reaction)
                else:
                    break

            except asyncio.TimeoutError:
                try:
                    await message.clear_reactions()
                except discord.errors.NotFound:
                    logger.warning(f"Message already deleted for player {player_id}.")
                except discord.errors.Forbidden:
                    logger.error(f"Bot lacks permissions to clear reactions for player {player_id}.")
                except Exception as e:
                    logger.error(f"Unexpected error while clearing reactions: {e}")
                break

@app_commands.command(name="showequipment", description="Display your equipped monster and deck")
async def show_equipment(interaction: discord.Interaction):
    player_id = interaction.user.id
    if player_id not in player_monsters:
        await interaction.response.send_message("You need to equip a monster first! Contact an admin with /monster.", ephemeral=True)
        return

    monster = player_monsters[player_id]
    deck = player_decks.get(player_id, [])
    attack_bonus, hp_bonus, energy_bonus, defence_bonus, hp_per_turn, energy_per_turn, prevent_freeze, last_turn_when_dead = calculate_bonuses(deck)

    # Appliquer les bonus d'attributs avec arrondi vers le haut
    player_stat = player_stats.get(player_id, {"attributes": {"damage": 0, "hp": 0, "energy": 0, "defence": 0}})
    attack_bonus_with_attributes = math.ceil(attack_bonus + player_stat["attributes"]["damage"] * 0.75)
    hp_with_attributes = math.ceil(monster["hp"] + hp_bonus + player_stat["attributes"]["hp"] * 2.50)
    energy_with_attributes = math.ceil(monster["energy"] + energy_bonus + player_stat["attributes"]["energy"] * 2)
    defence_with_attributes = math.ceil(monster["defence"] + defence_bonus + player_stat["attributes"]["defence"] * 0.5)

    # Calculer le poids total avec la réduction potentielle
    total_weight = calculate_total_weight(deck)

    embed = Embed(title=f"{interaction.user.display_name}'s Monster and Deck", color=discord.Color.green())
    embed.set_thumbnail(url=monster["image_url"])
    stats = (
        f"**Damage:** {monster['attack']} + {attack_bonus_with_attributes}\n"
        f"**HP:** {hp_with_attributes} +{hp_per_turn}\n"
        f"**Energy:** {energy_with_attributes} +{energy_per_turn}\n"
        f"**Defense:** {defence_with_attributes}"
    )
    embed.add_field(name="Stats", value=stats, inline=False)
    equipment_str = "\n".join(f"- {item['name']} ({item['type']})" for item in deck if item["type"] != "Spell") or "None"
    embed.add_field(name="Equipment", value=equipment_str, inline=False)
    spell_str = "\n".join(f"- {item['name']}" for item in deck if item["type"] == "Spell") or "None"
    embed.add_field(name="Spells", value=spell_str, inline=False)
    embed.add_field(name="Total Weight", value=f"{total_weight}/10", inline=False)

    await interaction.response.send_message(embed=embed)
    message = await interaction.original_response()
    await asyncio.sleep(180)
    await message.delete()

@app_commands.command(name="monster_board", description="Check where to view the leaderboard")
async def monster_board(interaction: discord.Interaction):
    leaderboard_channel_id = 1361436181442596944
    await interaction.response.send_message(f"Consultez le classement dans le canal <#{leaderboard_channel_id}> !")
    message = await interaction.original_response()
    await asyncio.sleep(180)
    await message.delete()
