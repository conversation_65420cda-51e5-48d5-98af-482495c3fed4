from discord import app_commands
import logging
logger = logging.getLogger(__name__)

def calculate_bonuses(deck):
    # Calculer les bonus en ignorant les sorts
    attack_bonus = sum(item.get("attack_bonus", 0) for item in deck if item["type"] != "Spell")
    hp_bonus = sum(item.get("hp_bonus", 0) for item in deck if item["type"] != "Spell")
    energy_bonus = sum(item.get("energy_bonus", 0) for item in deck if item["type"] != "Spell")
    defence_bonus = sum(item.get("defence_bonus", 0) for item in deck if item["type"] != "Spell")
    energy_per_turn = sum(item.get("energy_per_turn", 0) for item in deck if item["type"] != "Spell")
    hp_per_turn = sum(item.get("hp_per_turn", 0) for item in deck if item["type"] != "Spell")
    
    # Vérifier la présence de Blood Ring pour Red Flower
    has_blood_ring = any(item["name"] == "Blood Ring" for item in deck if item["type"] != "Spell")
    for item in deck:
        if item["type"] != "Spell" and item["name"] == "Red Flower" and has_blood_ring:
            hp_per_turn += 1  # +1 HP par tour si Blood Ring est présent
    
    # Compter le nombre de The Lost Page
    lost_page_count = sum(1 for item in deck if item["name"] == "The Lost Page" and item["type"] != "Spell")
    
    # Compter le nombre de livres (de n'importe quel type)
    book_count = sum(1 for item in deck if item["name"] in ["Book of Aeralis", "Book of Life", "Book of Strength"] and item["type"] != "Spell")
    
    # Si on a au moins un livre, chaque Lost Page donne +3 HP et +3 Energy
    if book_count > 0 and lost_page_count > 0:
        # Chaque Lost Page peut être combiné avec n'importe quel livre
        hp_bonus += 3 * lost_page_count
        energy_bonus += 3 * lost_page_count
    
    # Vérifier la présence de Mystic Pearl et Ring of Diversity
    has_mystic_pearl = any(item["name"] == "Mystic Pearl" for item in deck if item["type"] != "Spell")
    has_ring_of_diversity = any(item["name"] == "Ring of Diversity" for item in deck if item["type"] != "Spell")
    
    # Si les deux sont présents, ajouter les bonus
    if has_mystic_pearl and has_ring_of_diversity:
        hp_bonus += 3
        energy_bonus += 3
    
    # Vérifier si le joueur est immunisé au gel
    prevent_freeze = any(item.get("prevent_freeze", False) for item in deck if item["type"] != "Spell")
    
    return attack_bonus, hp_bonus, energy_bonus, defence_bonus, hp_per_turn, energy_per_turn, prevent_freeze

def calculate_total_weight(deck):
    # Vérifier si Crystal Powder peut réduire le poids d'un bouclier
    has_crystal_powder = any(item["name"] == "Crystal Powder" for item in deck)
    has_perfect_ruby = any(item["name"] == "Perfect Ruby" for item in deck)
    has_perfect_sapphire = any(item["name"] == "Perfect Sapphire" for item in deck)
    has_combat_shield = any(item["name"] == "Combat Shield" for item in deck)
    has_shield_of_light = any(item["name"] == "Shield of Light" for item in deck)
    
    # Vérifier si Jewel of Light est présent
    has_jewel_of_light = any(item["name"] == "Jewel of Light" for item in deck)
    
    # Calculer la réduction de poids
    weight_reduction = 0
    if has_crystal_powder:
        if (has_perfect_ruby or has_perfect_sapphire) and (has_combat_shield or has_shield_of_light):
            weight_reduction += 1
    
    # Ajouter la réduction de Jewel of Light
    if has_jewel_of_light:
        weight_reduction += 1
    
    # Calculer le poids total avec la réduction
    raw_weight = sum(item["weight"] for item in deck)
    total_weight = raw_weight - weight_reduction
    
    return max(0, total_weight)  # Assurer que le poids ne devient pas négatif

def get_choices(equipment_type, equipments):
    """Génère les choix pour les menus déroulants par type."""
    choices = [app_commands.Choice(name=f"{item['name']} (Poids: {item['weight']})", value=item['name']) 
               for item in equipments if item['type'] == equipment_type]
    choices.insert(0, app_commands.Choice(name="Aucun", value="None"))
    return choices

def get_all_choices(equipments):
    """Génère tous les choix pour ajouter un équipement."""
    return [app_commands.Choice(name=f"{item['name']} (Type: {item['type']}, Poids: {item['weight']})", value=item['name']) 
            for item in equipments]

print("Utils.py est OK, toutes les fonctions sont définies !")
